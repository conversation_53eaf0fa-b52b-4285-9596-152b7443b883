package battle

import (
	"context"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"

	"github.com/heroiclabs/nakama-common/runtime"
	"google.golang.org/protobuf/proto"
)

// 通知队伍所有成员加入战斗
func notifyAllTeamMembers(
	ctx context.Context,
	logger runtime.Logger,
	nk runtime.NakamaModule,
	matchId string,
	proposer *MainServer.Trainer,
	target *MainServer.Trainer,
	proposerParty *MainServer.PartyInfo,
	targetParty *MainServer.PartyInfo,
	proposerInParty bool,
	targetInParty bool,
) error {
	// 创建通知内容，使用脱敏的训练师信息
	notification := &MainServer.MatchJoinNotification{
		MatchId:  matchId,
		Proposer: tool.CopySafeTrainer(proposer),
		Target:   tool.CopySafeTrainer(target),
	}

	// 如果发起者在队伍中，通知队伍所有成员
	if proposerInParty {
		for _, member := range proposerParty.Trainers {
			// 跳过队长，因为队长已经在createBattleMatch中收到通知
			if member.Id == proposer.Id {
				continue
			}

			// 序列化通知
			notificationBytes, err := proto.Marshal(notification)
			if err != nil {
				logger.Error("序列化通知失败: %v", err)
				continue
			}

			// 发送通知给队伍成员
			content := map[string]interface{}{
				"data": notificationBytes,
			}

			if err := nk.NotificationSend(ctx, member.Uid, "match_join", content, int(MainServer.ServerNotificationType_ServerNotificationType_MatchJoin), "", true); err != nil {
				logger.Error("发送通知给发起者队伍成员失败: %v", err)
				// 继续通知其他成员，不中断流程
			}
		}
	}

	// 如果目标在队伍中，通知队伍所有成员
	if targetInParty {
		for _, member := range targetParty.Trainers {
			// 跳过队长，因为队长已经在createBattleMatch中收到通知
			if member.Id == target.Id {
				continue
			}

			// 序列化通知
			notificationBytes, err := proto.Marshal(notification)
			if err != nil {
				logger.Error("序列化通知失败: %v", err)
				continue
			}

			// 发送通知给队伍成员
			content := map[string]interface{}{
				"data": notificationBytes,
			}

			if err := nk.NotificationSend(ctx, member.Uid, "match_join", content, int(MainServer.ServerNotificationType_ServerNotificationType_MatchJoin), "", true); err != nil {
				logger.Error("发送通知给目标队伍成员失败: %v", err)
				// 继续通知其他成员，不中断流程
			}
		}
	}

	return nil
}
