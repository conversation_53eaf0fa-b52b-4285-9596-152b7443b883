package battle

import (
	"context"
	"database/sql"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"go-nakama-poke/trainer"

	"github.com/heroiclabs/nakama-common/runtime"
)

func isNpcBattle(battleType MainServer.BattleType) bool {
	switch battleType {
	case MainServer.BattleType_DoubleNPC, MainServer.BattleType_SingleNPC, MainServer.BattleType_SingleNPCAndDoublePokemon:
		return true
	}
	return false
}
func BattleEndSettlement(ctx context.Context, logger runtime.Logger, tx *sql.Tx, mState *PokeMatchState, battleInfo *tool.BattleInfo, winnerIds map[int64]bool, win *[]*MainServer.Poke, lose *[]*MainServer.Poke) (*MainServer.PokeExpInfos, error) {
	if battleInfo.BattleReward != nil {
		for _, v := range mState.presences {
			if ok, ex := winnerIds[v.trainer.Id]; ok && ex { //赢了才发放奖励
				//发放奖励
				trainer.ClaimReward(ctx, logger, tx, v.trainer, battleInfo.BattleReward)
			}
		}
	}
	return battleExpSettlement(ctx, logger, tx, win, lose)
}

// 战斗经验结算
func battleExpSettlement(ctx context.Context, logger runtime.Logger, tx *sql.Tx, wins *[]*MainServer.Poke, loses *[]*MainServer.Poke) (*MainServer.PokeExpInfos, error) {
	expInfos := &MainServer.PokeExpInfos{}
	expInfos.Infos = make([]*MainServer.PokeExpInfo, 0)
	for _, wpoke := range *wins {
		wpoke.Extra.LastExpInfo = &MainServer.PokeExpInfo{}
		for _, lpoke := range *loses {
			lpokemonData, exists := poke.GetPokemonInfo(lpoke.Name)
			if !exists {
				return nil, runtime.NewError("无效的poke", 500)
			}
			addEfforts(wpoke, lpokemonData)
			if wpoke.Level < 100 {
				wpoke.Extra.LastExpInfo.PokeId = wpoke.Id
				wpoke.Extra.LastExpInfo.PokeName = wpoke.Name
				wpoke.Extra.LastExpInfo.OldLevel = wpoke.Level
				wpoke.Extra.LastExpInfo.OldExp = wpoke.Experience
				ex := poke.CalculateExperience(float64(lpokemonData.BaseExperience), float64(lpoke.Level), float64(wpoke.Level), 1, 1, 1, 1, 1, 1, 1) * 30
				wpoke.Extra.LastExpInfo.Exp = int32(ex)
				wpoke.Experience += int64(ex)
				// oldLevel := wpoke.Level
				configLevel(wpoke)
				wpoke.Extra.LastExpInfo.Level = wpoke.Level
				if wpoke.Extra.LastExpInfo.OldLevel != wpoke.Level {
					moves := getPokeLevelUpMoves(wpoke.Name, wpoke.Extra.LastExpInfo.OldLevel+1, wpoke.Extra.LastExpInfo.Level)
					if len(moves) > 0 {
						wpoke.Extra.LastExpInfo.MoveIds = moves
					}
				}
			}
		}
		if wpoke.Born != nil {
			wpoke.Born.IsBattle = true
		} else {
			wpoke.Born = &MainServer.BornInfo{
				IsBattle: true,
			}
		}
		expInfos.Infos = append(expInfos.Infos, wpoke.Extra.LastExpInfo)
		poke.UpdatePokeData(ctx, tx, wpoke)
		//等级
	}
	for _, lpoke := range *loses {
		if lpoke.Tid > 0 && lpoke.Id > 0 {
			lpoke.Extra.LastExpInfo = &MainServer.PokeExpInfo{}
			if lpoke.Born != nil {
				lpoke.Born.IsBattle = true
			} else {
				lpoke.Born = &MainServer.BornInfo{
					IsBattle: true,
				}
			}
			poke.UpdatePokeData(ctx, tx, lpoke)
		}
	}
	return expInfos, nil
}
func getPokeLevelUpMoves(pokeName string, minLevel int32, maxLevel int32) []string {
	movefilter := &poke.MoveFilter{
		PokemonName: pokeName,
		Gen:         0,
		Level:       maxLevel,
		MinLevel:    minLevel,
		Methods:     []MainServer.MoveLearnMethod{MainServer.MoveLearnMethod_LEVEL_UP},
		IsInit:      false,
	}
	return poke.GetFilteredMoves(*movefilter)
}
func configLevel(wpoke *MainServer.Poke) error {
	wpokemonData, exists := poke.GetPokemonInfo(wpoke.Name)
	if !exists {
		return runtime.NewError("无效的wpoke: 找不到对应的宝可梦数据", 500)
	}

	// 获取成长率数据
	growthRate, exists := poke.GetPokemonGrowthRate(wpokemonData.GrowthRate)
	if !exists {
		return runtime.NewError("无效的growthRate: 找不到对应的成长率数据", 500)
	}
	for i := wpoke.Level; i < 100; i++ {
		if wpoke.Experience-int64(growthRate.Levels[i]) < 0 {
			wpoke.Level = i
			return nil
		}
	}
	return nil
}
func addEfforts(wpoke *MainServer.Poke, pokemonData *MainServer.PSPokemonData) {
	if wpoke.Evs.Hp+wpoke.Evs.Atk+wpoke.Evs.Def+wpoke.Evs.Spa+wpoke.Evs.Spd+wpoke.Evs.Spe >= 512 {
		return
	}
	wpoke.Evs.Hp += pokemonData.Efforts.Hp
	if wpoke.Evs.Hp > 255 {
		wpoke.Evs.Hp = 255
	}
	wpoke.Evs.Atk += pokemonData.Efforts.Atk
	if wpoke.Evs.Atk > 255 {
		wpoke.Evs.Atk = 255
	}
	wpoke.Evs.Def += pokemonData.Efforts.Def
	if wpoke.Evs.Def > 255 {
		wpoke.Evs.Def = 255
	}
	wpoke.Evs.Spa += pokemonData.Efforts.Spa
	if wpoke.Evs.Spa > 255 {
		wpoke.Evs.Spa = 255
	}
	wpoke.Evs.Spd += pokemonData.Efforts.Spd
	if wpoke.Evs.Spd > 255 {
		wpoke.Evs.Spd = 255
	}
	wpoke.Evs.Spe += pokemonData.Efforts.Spe
	if wpoke.Evs.Spe > 255 {
		wpoke.Evs.Spe = 255
	}
}
