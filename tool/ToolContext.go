package tool

import (
	"context"
	"go-nakama-poke/proto/MainServer"
	"strconv"
	"sync"

	"github.com/heroiclabs/nakama-common/runtime"
)

var (
	_userActiveMap  = make(map[string]*MainServer.Trainer) // 以用户ID为键
	_trainerIdMap   = make(map[string]*MainServer.Trainer) // 以Trainer.Id为键
	trainerMapMutex sync.RWMutex                           // 读写锁，确保并发安全
)
var (
	_trainerAroundPokesMap = make(map[string][]*MainServer.Poke)
	aroundPokesMapMutex    sync.RWMutex
)
var (
	_trainerQuestMap = make(map[string]map[string]*MainServer.TrainerQuest) // 以Trainer.Id为键
	questMutex       sync.RWMutex
)

func AddOrUpdateTrainerQuest(trainerId string, quest *MainServer.TrainerQuest) {
	questMutex.Lock()
	defer questMutex.Unlock()
	if _, exists := _trainerQuestMap[trainerId]; !exists {
		_trainerQuestMap[trainerId] = make(map[string]*MainServer.TrainerQuest)
	}
	_trainerQuestMap[trainerId][quest.QuestId] = quest
}

// questId 不是 quest.Id
func RemoveTrainerQuest(trainerId string, questId int64) {
	questMutex.Lock()
	defer questMutex.Unlock()
	if _, exists := _trainerQuestMap[trainerId]; !exists {
		return
	}
	delete(_trainerQuestMap[trainerId], strconv.FormatInt(int64(questId), 10))
}

// 添加或更新 trainer 的 Pokemon
func AddOrUpdateTrainerAroundPokes(tid int64, pokes []*MainServer.Poke) {
	idstr := strconv.FormatInt(tid, 10)
	aroundPokesMapMutex.Lock()
	defer aroundPokesMapMutex.Unlock()
	_trainerAroundPokesMap[idstr] = pokes
}

// 删除 trainer 的 Pokemon
func RemoveTrainerAroundPokes(tid int64) {
	idstr := strconv.FormatInt(tid, 10)
	aroundPokesMapMutex.Lock()
	defer aroundPokesMapMutex.Unlock()
	delete(_trainerAroundPokesMap, idstr)
}
func RemoveTrainerAroundPokesBy(tid string) {
	aroundPokesMapMutex.Lock()
	defer aroundPokesMapMutex.Unlock()
	delete(_trainerAroundPokesMap, tid)
}

// 获取 trainer 的 Pokemon
func GetTrainerAroundPokes(tid int64) ([]*MainServer.Poke, bool) {
	idstr := strconv.FormatInt(tid, 10)
	aroundPokesMapMutex.RLock()
	defer aroundPokesMapMutex.RUnlock()
	pokes, exists := _trainerAroundPokesMap[idstr]
	return pokes, exists
}

// PokeIds:     append([]string{}, trainer.PokeIds...), // 深拷贝切片
// Badges:      append([]string{}, trainer.Badges...), // 深拷贝切片
func GetDesensitizeActiveTrainersByIds(ids []string) []*MainServer.Trainer {
	trainerMapMutex.RLock()         // 加读锁
	defer trainerMapMutex.RUnlock() // 解锁

	var trainers []*MainServer.Trainer
	for _, id := range ids {
		trainer, exists := _trainerIdMap[id]
		if exists {
			// 深拷贝 trainer 对象
			desensitizedTrainer := CopySafeTrainer(trainer)

			trainers = append(trainers, desensitizedTrainer)
		} else {
			RemoveActiveTrainerIdStr(id, false)
		}
	}
	return trainers
}

// GetActiveTrainersByIds 根据 Trainer.Id 获取所有 Trainer
func GetSafeActiveTrainersByIds(ids []int64) []*MainServer.Trainer {
	trainerMapMutex.RLock()         // 加读锁
	defer trainerMapMutex.RUnlock() // 解锁

	var trainers []*MainServer.Trainer
	for _, id := range ids {
		if id < 0 {
			continue
		}
		idstr := strconv.FormatInt(id, 10)
		trainer, exists := _trainerIdMap[idstr]
		if exists {
			trainers = append(trainers, CopySafeTrainer(trainer))
		} else { //可能死锁
			RemoveActiveTrainerIdStr(idstr, false)
		}
	}
	return trainers
}
func GetSafeActiveTrainersByUids(uids []string) []*MainServer.Trainer {
	trainerMapMutex.RLock()         // 加读锁
	defer trainerMapMutex.RUnlock() // 解锁

	var trainers []*MainServer.Trainer
	for _, idstr := range uids {
		trainer, exists := _userActiveMap[idstr]
		if exists {
			trainers = append(trainers, CopySafeTrainer(trainer))
		} else { //可能死锁
			RemoveUserActiveTrainer(idstr, false)
		}
	}
	return trainers
}
func GetActiveTrainersBase64ByIds(ids []string) []string {
	trainerMapMutex.RLock()         // 加读锁
	defer trainerMapMutex.RUnlock() // 解锁

	var trainers []string
	for _, id := range ids {
		if trainer, exists := _trainerIdMap[id]; exists {
			baseStr, err := ProtoToBase64(trainer)
			if err == nil {
				trainers = append(trainers, baseStr)
			}
		}
	}
	return trainers
}

// GetActiveTrainerByCtx 根据上下文获取用户的活跃 Trainer
func GetActiveTrainerByCtx(ctx context.Context) *MainServer.Trainer {
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	return GetActiveTrainerByUid(userID)
}
func GetActiveTrainerByTid(tid int64) *MainServer.Trainer {
	trainerMapMutex.RLock()         // 加读锁
	defer trainerMapMutex.RUnlock() // 解锁
	idstr := strconv.FormatInt(tid, 10)
	return _trainerIdMap[idstr]
}

// GetActiveTrainerByUid 获取用户的活跃 Trainer
func GetActiveTrainerByUid(userID string) *MainServer.Trainer {
	// trainerMapMutex.RLock()         // 加读锁
	// defer trainerMapMutex.RUnlock() // 解锁

	// return _userActiveMap[userID]
	trainerMapMutex.RLock()
	t, ok := _userActiveMap[userID]
	trainerMapMutex.RUnlock()
	if !ok {
		return nil
	}
	return t
}

// func GetActiveTrainerIdByUid(userID string) *MainServer.Trainer {
// 	trainerMapMutex.RLock()         // 加读锁
// 	defer trainerMapMutex.RUnlock() // 解锁

//		return _userActiveMap[userID]
//	}
func RemoveActiveTrainerIdStr(tid string, lock bool) bool {
	if lock {
		trainerMapMutex.Lock()         // 加写锁
		defer trainerMapMutex.Unlock() // 解锁
	}

	// 检查用户是否存在
	if trainer, exists := _trainerIdMap[tid]; exists {
		delete(_userActiveMap, trainer.Uid)
		delete(_trainerIdMap, tid) // 同时从 _trainerIdMap 中删除
		RemoveTrainerAroundPokesBy(tid)
		return true
	}
	return false
}
func RemoveActiveTrainer(tid int64, lock bool) bool {
	idstr := strconv.FormatInt(tid, 10)
	if lock {
		trainerMapMutex.Lock()         // 加写锁
		defer trainerMapMutex.Unlock() // 解锁
	}

	// 检查用户是否存在
	if trainer, exists := _trainerIdMap[idstr]; exists {
		delete(_userActiveMap, trainer.Uid)
		delete(_trainerIdMap, idstr) // 同时从 _trainerIdMap 中删除
		RemoveTrainerAroundPokesBy(idstr)
		return true
	}
	return false
}

// RemoveUserActiveTrainer 删除用户的活跃 Trainer
func RemoveUserActiveTrainer(userID string, lock bool) bool {
	if lock {
		trainerMapMutex.Lock()         // 加写锁
		defer trainerMapMutex.Unlock() // 解锁
	}

	// 检查用户是否存在
	if trainer, exists := _userActiveMap[userID]; exists {
		delete(_userActiveMap, userID)
		tidstr := strconv.FormatInt(trainer.Id, 10)
		delete(_trainerIdMap, tidstr) // 同时从 _trainerIdMap 中删除
		RemoveTrainerAroundPokesBy(tidstr)
		return true
	}
	return false
}

// SetUserActiveTrainer 设置用户的活跃 Trainer，并维护 _trainerIdMap
func SetUserActiveTrainer(userID string, trainer *MainServer.Trainer) {
	trainerMapMutex.Lock()         // 加写锁
	defer trainerMapMutex.Unlock() // 解锁

	// 如果用户已存在，需更新 _trainerIdMap
	if existingTrainer, exists := _userActiveMap[userID]; exists {
		delete(_trainerIdMap, strconv.FormatInt(existingTrainer.Id, 10)) // 删除旧的 Trainer.Id
	}

	_userActiveMap[userID] = trainer
	_trainerIdMap[strconv.FormatInt(trainer.Id, 10)] = trainer // 添加新的 Trainer.Id 映射
}
