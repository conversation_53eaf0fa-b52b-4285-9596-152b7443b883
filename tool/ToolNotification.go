package tool

import (
	"context"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
	"google.golang.org/protobuf/proto"
)

// 发送战斗邀请通知
func SendInviteBattleNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, proposer *MainServer.Trainer, target *MainServer.Trainer, inviteType MainServer.InviteBattleType) error {
	// 创建通知内容，使用脱敏的训练师信息
	notification := &MainServer.InviteBattleNotification{
		Proposer:   CopySafeTrainer(proposer),
		InviteType: inviteType,
	}

	// 序列化通知
	notificationBytes, err := proto.Marshal(notification)
	if err != nil {
		logger.Error("序列化通知失败: %v", err)
		return err
	}

	// 发送通知
	content := map[string]interface{}{
		"data": notificationBytes,
	}

	return nk.NotificationSend(ctx, target.Uid, "invite_battle", content, int(MainServer.ServerNotificationType_ServerNotificationType_InviteBattle), proposer.Uid, true)
}

// 发送Match加入通知
func SendMatchJoinNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, matchId string, proposer *MainServer.Trainer, target *MainServer.Trainer) error {
	// 创建通知内容，使用脱敏的训练师信息
	notification := &MainServer.MatchJoinNotification{
		MatchId:  matchId,
		Proposer: CopySafeTrainer(proposer),
		Target:   CopySafeTrainer(target),
	}

	// 序列化通知
	notificationBytes, err := proto.Marshal(notification)
	if err != nil {
		logger.Error("序列化通知失败: %v", err)
		return err
	}

	// 发送通知内容
	content := map[string]interface{}{
		"data": notificationBytes,
	}

	// 发送给发起者
	if err := nk.NotificationSend(ctx, proposer.Uid, "match_join", content, int(MainServer.ServerNotificationType_ServerNotificationType_MatchJoin), "", true); err != nil {
		logger.Error("发送通知给发起者失败: %v", err)
		return err
	}

	// 发送给接收者
	if err := nk.NotificationSend(ctx, target.Uid, "match_join", content, int(MainServer.ServerNotificationType_ServerNotificationType_MatchJoin), "", true); err != nil {
		logger.Error("发送通知给接收者失败: %v", err)
		return err
	}

	return nil
}
func SendSwopInfoNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, swop *MainServer.SwopInfo) error {
	trainer := GetActiveTrainerByCtx(ctx)
	if trainer == nil {
		return runtime.NewError("未找到训练师信息", 400)
	}
	// 获取发起者和目标训练师
	// initiator := tool.GetActiveTrainerByTid(swop.InitiatorId)
	// target := tool.GetActiveTrainerByTid(swop.TargetId)
	// if initiator == nil || target == nil {
	// 	logger.Error("发送交换请求通知失败: 无法获取训练师信息")
	// 	return runtime.NewError("无法获取训练师信息", 404)
	// }

	// // 创建通知内容，使用脱敏的训练师信息
	// notification := &MainServer.SwopRequestNotification{
	// 	SwopId:    swop.SwopId,
	// 	Initiator: tool.CopySafeTrainer(initiator),
	// 	Target:    tool.CopySafeTrainer(target),
	// 	State:     swop.InitiatorState, // 使用发起方状态
	// 	CreateTs:  swop.CreateTs,
	// 	UpdateTs:  swop.UpdateTs,
	// }
	notification := &MainServer.SwopInfoNotification{
		Swop:   swop,
		Sender: CopySafeTrainer(trainer),
	}

	initiatorTrainer := GetActiveTrainerByTid(swop.InitiatorId)
	if initiatorTrainer == nil {
		return runtime.NewError("未找到发起方训练师信息", 400)
	}
	targetTrainer := GetActiveTrainerByTid(swop.TargetId)
	if targetTrainer == nil {
		return runtime.NewError("未找到目标训练师信息", 400)
	}
	// 序列化通知
	notificationBytes, err := proto.Marshal(notification)
	if err != nil {
		logger.Error("序列化通知失败: %v", err)
		return err
	}

	// 发送通知内容
	content := map[string]interface{}{
		"data": notificationBytes,
	}
	sender := initiatorTrainer
	receiver := targetTrainer
	if trainer.Id == swop.TargetId {
		sender = targetTrainer
		receiver = initiatorTrainer
	}

	if err := nk.NotificationSend(ctx, receiver.Uid, "swop_info", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopInfo), sender.Uid, true); err != nil {
		logger.Error("发送SendSwopInfoNotification通知失败: %v", err)
		return err
	}
	// 发送给发起者
	// if err := nk.NotificationSend(ctx, initiator.Uid, "swop_request", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopRequest), "", true); err != nil {
	// 	logger.Error("发送通知给发起者失败: %v", err)
	// 	return err
	// }

	// // 发送给接收者
	// if err := nk.NotificationSend(ctx, target.Uid, "swop_request", content, int(MainServer.ServerNotificationType_ServerNotificationType_SwopRequest), "", true); err != nil {
	// 	logger.Error("发送通知给接收者失败: %v", err)
	// 	return err
	// }

	return nil
}

// 发送组队邀请通知
func SendInvitePartyNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, proposer *MainServer.Trainer, target *MainServer.Trainer) error {
	// 创建通知内容，使用脱敏的训练师信息
	notification := &MainServer.InvitePartyNotification{
		Proposer:   CopySafeTrainer(proposer),
		IsWantJoin: false,
	}

	// 序列化通知
	notificationBytes, err := proto.Marshal(notification)
	if err != nil {
		logger.Error("序列化通知失败: %v", err)
		return err
	}

	// 发送通知
	content := map[string]interface{}{
		"data": notificationBytes,
	}

	return nk.NotificationSend(ctx, target.Uid, "invite_party", content, int(MainServer.ServerNotificationType_ServerNotificationType_InviteParty), proposer.Uid, true)
}

// 发送参与组队通知 //用系统的方法就行
// func SendWantJoinPartyNotification(ctx context.Context, logger runtime.Logger, nk runtime.NakamaModule, proposer *MainServer.Trainer, target *MainServer.Trainer) error {
// 	// 创建通知内容，使用脱敏的训练师信息
// 	notification := &MainServer.InvitePartyNotification{
// 		Proposer:   CopySafeTrainer(proposer),
// 		IsWantJoin: true,
// 	}

// 	// 序列化通知
// 	notificationBytes, err := proto.Marshal(notification)
// 	if err != nil {
// 		logger.Error("序列化通知失败: %v", err)
// 		return err
// 	}

// 	// 发送通知
// 	content := map[string]interface{}{
// 		"data": notificationBytes,
// 	}

// 	return nk.NotificationSend(ctx, target.Uid, "want_join_party", content, int(MainServer.ServerNotificationType_ServerNotificationType_InviteParty), proposer.Uid, true)
// }
