// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Equipment.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EquipmentType int32

const (
	EquipmentType_equipment_nor      EquipmentType = 0
	EquipmentType_equipment_title    EquipmentType = 1
	EquipmentType_equipment_card     EquipmentType = 2 //卡片（素材多（对应title称号
	EquipmentType_equipment_ride     EquipmentType = 3 //坐骑
	EquipmentType_equipment_pokeball EquipmentType = 4
	EquipmentType_equipment_badge    EquipmentType = 5
	EquipmentType_equipment_amulet   EquipmentType = 6 //护符
)

// Enum value maps for EquipmentType.
var (
	EquipmentType_name = map[int32]string{
		0: "equipment_nor",
		1: "equipment_title",
		2: "equipment_card",
		3: "equipment_ride",
		4: "equipment_pokeball",
		5: "equipment_badge",
		6: "equipment_amulet",
	}
	EquipmentType_value = map[string]int32{
		"equipment_nor":      0,
		"equipment_title":    1,
		"equipment_card":     2,
		"equipment_ride":     3,
		"equipment_pokeball": 4,
		"equipment_badge":    5,
		"equipment_amulet":   6,
	}
)

func (x EquipmentType) Enum() *EquipmentType {
	p := new(EquipmentType)
	*p = x
	return p
}

func (x EquipmentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EquipmentType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Equipment_proto_enumTypes[0].Descriptor()
}

func (EquipmentType) Type() protoreflect.EnumType {
	return &file_MainServer_Equipment_proto_enumTypes[0]
}

func (x EquipmentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EquipmentType.Descriptor instead.
func (EquipmentType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{0}
}

type FortifyMaterialType int32

const (
	FortifyMaterialType_FortifyMaterialType_Unknown  FortifyMaterialType = 0
	FortifyMaterialType_FortifyMaterialType_Item     FortifyMaterialType = 1
	FortifyMaterialType_FortifyMaterialType_Euipment FortifyMaterialType = 2
	FortifyMaterialType_FortifyMaterialType_Poke     FortifyMaterialType = 3
)

// Enum value maps for FortifyMaterialType.
var (
	FortifyMaterialType_name = map[int32]string{
		0: "FortifyMaterialType_Unknown",
		1: "FortifyMaterialType_Item",
		2: "FortifyMaterialType_Euipment",
		3: "FortifyMaterialType_Poke",
	}
	FortifyMaterialType_value = map[string]int32{
		"FortifyMaterialType_Unknown":  0,
		"FortifyMaterialType_Item":     1,
		"FortifyMaterialType_Euipment": 2,
		"FortifyMaterialType_Poke":     3,
	}
)

func (x FortifyMaterialType) Enum() *FortifyMaterialType {
	p := new(FortifyMaterialType)
	*p = x
	return p
}

func (x FortifyMaterialType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FortifyMaterialType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Equipment_proto_enumTypes[1].Descriptor()
}

func (FortifyMaterialType) Type() protoreflect.EnumType {
	return &file_MainServer_Equipment_proto_enumTypes[1]
}

func (x FortifyMaterialType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FortifyMaterialType.Descriptor instead.
func (FortifyMaterialType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{1}
}

// 库存状态枚举
type EquipmentStatus int32

const (
	EquipmentStatus_EquipmentStatus_Unknown EquipmentStatus = 0
	EquipmentStatus_EquipmentStatus_Normal  EquipmentStatus = 1 // 普通状态
	EquipmentStatus_EquipmentStatus_Sale    EquipmentStatus = 2 // 上架状态
	EquipmentStatus_EquipmentStatus_Equip   EquipmentStatus = 3 // 装备状态
)

// Enum value maps for EquipmentStatus.
var (
	EquipmentStatus_name = map[int32]string{
		0: "EquipmentStatus_Unknown",
		1: "EquipmentStatus_Normal",
		2: "EquipmentStatus_Sale",
		3: "EquipmentStatus_Equip",
	}
	EquipmentStatus_value = map[string]int32{
		"EquipmentStatus_Unknown": 0,
		"EquipmentStatus_Normal":  1,
		"EquipmentStatus_Sale":    2,
		"EquipmentStatus_Equip":   3,
	}
)

func (x EquipmentStatus) Enum() *EquipmentStatus {
	p := new(EquipmentStatus)
	*p = x
	return p
}

func (x EquipmentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EquipmentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Equipment_proto_enumTypes[2].Descriptor()
}

func (EquipmentStatus) Type() protoreflect.EnumType {
	return &file_MainServer_Equipment_proto_enumTypes[2]
}

func (x EquipmentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EquipmentStatus.Descriptor instead.
func (EquipmentStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{2}
}

type TrainerEquipmentEffectType int32

const (
	TrainerEquipmentEffectType_EquipmentEffect_None TrainerEquipmentEffectType = 0
	// 野外遇敌
	TrainerEquipmentEffectType_EquipmentEffect_EncounterEnemy         TrainerEquipmentEffectType = 1 //遇敌概率 （这个还是不要了，或者单独给一个）
	TrainerEquipmentEffectType_EquipmentEffect_EncounterShine         TrainerEquipmentEffectType = 2 //遇闪概率
	TrainerEquipmentEffectType_EquipmentEffect_EncounterGoodEnemy     TrainerEquipmentEffectType = 3 //遇高v poke的概率
	TrainerEquipmentEffectType_EquipmentEffect_GetBattlePoke          TrainerEquipmentEffectType = 4 //捕获poke的概率
	TrainerEquipmentEffectType_EquipmentEffect_GetWildBattleEquipment TrainerEquipmentEffectType = 5 //野外战斗道具的概率
	TrainerEquipmentEffectType_EquipmentEffect_FishingGoodEquipment   TrainerEquipmentEffectType = 6 //钓鱼钓到好道具（金钱）的概率
	TrainerEquipmentEffectType_EquipmentEffect_FishingBattleEquipment TrainerEquipmentEffectType = 7 //钓到战斗道具的概率
	// 战斗
	TrainerEquipmentEffectType_EquipmentEffect_GetBattleExp   TrainerEquipmentEffectType = 101 //获得战斗经验的概率
	TrainerEquipmentEffectType_EquipmentEffect_GetBattleMoney TrainerEquipmentEffectType = 102 //获得战斗金钱的概率
	// 孵蛋
	TrainerEquipmentEffectType_EquipmentEffect_BreedGoodPokebaby  TrainerEquipmentEffectType = 201 //生育好宝宝的概率
	TrainerEquipmentEffectType_EquipmentEffect_BreedDifficultBaby TrainerEquipmentEffectType = 202 //父母难产的概率
	TrainerEquipmentEffectType_EquipmentEffect_FishingGoodPoke    TrainerEquipmentEffectType = 203 //钓鱼钓到好poke的概率
	TrainerEquipmentEffectType_EquipmentEffect_GetBattleEvs       TrainerEquipmentEffectType = 204 //获得战斗evs的概率
	TrainerEquipmentEffectType_EquipmentEffect_HatchEggTime       TrainerEquipmentEffectType = 205 //孵蛋时间
	TrainerEquipmentEffectType_EquipmentEffect_BreedShine         TrainerEquipmentEffectType = 206 //孵蛋遇闪概率
	// 家园
	TrainerEquipmentEffectType_EquipmentEffect_TreeFruitGrowup   TrainerEquipmentEffectType = 301 //树果成长速度
	TrainerEquipmentEffectType_EquipmentEffect_TreeFruitWithered TrainerEquipmentEffectType = 302 //树果枯萎速度概率
	// 其他
	TrainerEquipmentEffectType_EquipmentEffect_BorrowGoodPoke TrainerEquipmentEffectType = 901 //借到好poke的概率
	TrainerEquipmentEffectType_EquipmentEffect_TeamBind       TrainerEquipmentEffectType = 902 //绑定团队(会和团队的部分效果相关（比如特定的精灵（不单独生效需要其他效果辅助生效)
)

// Enum value maps for TrainerEquipmentEffectType.
var (
	TrainerEquipmentEffectType_name = map[int32]string{
		0:   "EquipmentEffect_None",
		1:   "EquipmentEffect_EncounterEnemy",
		2:   "EquipmentEffect_EncounterShine",
		3:   "EquipmentEffect_EncounterGoodEnemy",
		4:   "EquipmentEffect_GetBattlePoke",
		5:   "EquipmentEffect_GetWildBattleEquipment",
		6:   "EquipmentEffect_FishingGoodEquipment",
		7:   "EquipmentEffect_FishingBattleEquipment",
		101: "EquipmentEffect_GetBattleExp",
		102: "EquipmentEffect_GetBattleMoney",
		201: "EquipmentEffect_BreedGoodPokebaby",
		202: "EquipmentEffect_BreedDifficultBaby",
		203: "EquipmentEffect_FishingGoodPoke",
		204: "EquipmentEffect_GetBattleEvs",
		205: "EquipmentEffect_HatchEggTime",
		206: "EquipmentEffect_BreedShine",
		301: "EquipmentEffect_TreeFruitGrowup",
		302: "EquipmentEffect_TreeFruitWithered",
		901: "EquipmentEffect_BorrowGoodPoke",
		902: "EquipmentEffect_TeamBind",
	}
	TrainerEquipmentEffectType_value = map[string]int32{
		"EquipmentEffect_None":                   0,
		"EquipmentEffect_EncounterEnemy":         1,
		"EquipmentEffect_EncounterShine":         2,
		"EquipmentEffect_EncounterGoodEnemy":     3,
		"EquipmentEffect_GetBattlePoke":          4,
		"EquipmentEffect_GetWildBattleEquipment": 5,
		"EquipmentEffect_FishingGoodEquipment":   6,
		"EquipmentEffect_FishingBattleEquipment": 7,
		"EquipmentEffect_GetBattleExp":           101,
		"EquipmentEffect_GetBattleMoney":         102,
		"EquipmentEffect_BreedGoodPokebaby":      201,
		"EquipmentEffect_BreedDifficultBaby":     202,
		"EquipmentEffect_FishingGoodPoke":        203,
		"EquipmentEffect_GetBattleEvs":           204,
		"EquipmentEffect_HatchEggTime":           205,
		"EquipmentEffect_BreedShine":             206,
		"EquipmentEffect_TreeFruitGrowup":        301,
		"EquipmentEffect_TreeFruitWithered":      302,
		"EquipmentEffect_BorrowGoodPoke":         901,
		"EquipmentEffect_TeamBind":               902,
	}
)

func (x TrainerEquipmentEffectType) Enum() *TrainerEquipmentEffectType {
	p := new(TrainerEquipmentEffectType)
	*p = x
	return p
}

func (x TrainerEquipmentEffectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerEquipmentEffectType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Equipment_proto_enumTypes[3].Descriptor()
}

func (TrainerEquipmentEffectType) Type() protoreflect.EnumType {
	return &file_MainServer_Equipment_proto_enumTypes[3]
}

func (x TrainerEquipmentEffectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerEquipmentEffectType.Descriptor instead.
func (TrainerEquipmentEffectType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{3}
}

type TrainerEquipmentEffectPokeType int32

const (
	TrainerEquipmentEffectPokeType_EffectPoke_Unknown  TrainerEquipmentEffectPokeType = 0
	TrainerEquipmentEffectPokeType_EffectPoke_Normal   TrainerEquipmentEffectPokeType = 1
	TrainerEquipmentEffectPokeType_EffectPoke_Fire     TrainerEquipmentEffectPokeType = 2
	TrainerEquipmentEffectPokeType_EffectPoke_Water    TrainerEquipmentEffectPokeType = 3
	TrainerEquipmentEffectPokeType_EffectPoke_Electric TrainerEquipmentEffectPokeType = 4
	TrainerEquipmentEffectPokeType_EffectPoke_Grass    TrainerEquipmentEffectPokeType = 5
	TrainerEquipmentEffectPokeType_EffectPoke_Ice      TrainerEquipmentEffectPokeType = 6
	TrainerEquipmentEffectPokeType_EffectPoke_Fighting TrainerEquipmentEffectPokeType = 7
	TrainerEquipmentEffectPokeType_EffectPoke_Poison   TrainerEquipmentEffectPokeType = 8
	TrainerEquipmentEffectPokeType_EffectPoke_Ground   TrainerEquipmentEffectPokeType = 9
	TrainerEquipmentEffectPokeType_EffectPoke_Flying   TrainerEquipmentEffectPokeType = 10
	TrainerEquipmentEffectPokeType_EffectPoke_Psychic  TrainerEquipmentEffectPokeType = 11
	TrainerEquipmentEffectPokeType_EffectPoke_Bug      TrainerEquipmentEffectPokeType = 12
	TrainerEquipmentEffectPokeType_EffectPoke_Rock     TrainerEquipmentEffectPokeType = 13
	TrainerEquipmentEffectPokeType_EffectPoke_Ghost    TrainerEquipmentEffectPokeType = 14
	TrainerEquipmentEffectPokeType_EffectPoke_Dragon   TrainerEquipmentEffectPokeType = 15
	TrainerEquipmentEffectPokeType_EffectPoke_Dark     TrainerEquipmentEffectPokeType = 16
	TrainerEquipmentEffectPokeType_EffectPoke_Steel    TrainerEquipmentEffectPokeType = 17
	TrainerEquipmentEffectPokeType_EffectPoke_Fairy    TrainerEquipmentEffectPokeType = 18
	TrainerEquipmentEffectPokeType_EffectPoke_Stellar  TrainerEquipmentEffectPokeType = 19
)

// Enum value maps for TrainerEquipmentEffectPokeType.
var (
	TrainerEquipmentEffectPokeType_name = map[int32]string{
		0:  "EffectPoke_Unknown",
		1:  "EffectPoke_Normal",
		2:  "EffectPoke_Fire",
		3:  "EffectPoke_Water",
		4:  "EffectPoke_Electric",
		5:  "EffectPoke_Grass",
		6:  "EffectPoke_Ice",
		7:  "EffectPoke_Fighting",
		8:  "EffectPoke_Poison",
		9:  "EffectPoke_Ground",
		10: "EffectPoke_Flying",
		11: "EffectPoke_Psychic",
		12: "EffectPoke_Bug",
		13: "EffectPoke_Rock",
		14: "EffectPoke_Ghost",
		15: "EffectPoke_Dragon",
		16: "EffectPoke_Dark",
		17: "EffectPoke_Steel",
		18: "EffectPoke_Fairy",
		19: "EffectPoke_Stellar",
	}
	TrainerEquipmentEffectPokeType_value = map[string]int32{
		"EffectPoke_Unknown":  0,
		"EffectPoke_Normal":   1,
		"EffectPoke_Fire":     2,
		"EffectPoke_Water":    3,
		"EffectPoke_Electric": 4,
		"EffectPoke_Grass":    5,
		"EffectPoke_Ice":      6,
		"EffectPoke_Fighting": 7,
		"EffectPoke_Poison":   8,
		"EffectPoke_Ground":   9,
		"EffectPoke_Flying":   10,
		"EffectPoke_Psychic":  11,
		"EffectPoke_Bug":      12,
		"EffectPoke_Rock":     13,
		"EffectPoke_Ghost":    14,
		"EffectPoke_Dragon":   15,
		"EffectPoke_Dark":     16,
		"EffectPoke_Steel":    17,
		"EffectPoke_Fairy":    18,
		"EffectPoke_Stellar":  19,
	}
)

func (x TrainerEquipmentEffectPokeType) Enum() *TrainerEquipmentEffectPokeType {
	p := new(TrainerEquipmentEffectPokeType)
	*p = x
	return p
}

func (x TrainerEquipmentEffectPokeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainerEquipmentEffectPokeType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Equipment_proto_enumTypes[4].Descriptor()
}

func (TrainerEquipmentEffectPokeType) Type() protoreflect.EnumType {
	return &file_MainServer_Equipment_proto_enumTypes[4]
}

func (x TrainerEquipmentEffectPokeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainerEquipmentEffectPokeType.Descriptor instead.
func (TrainerEquipmentEffectPokeType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{4}
}

type EquipmentSlot struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	EquipmentId       string                 `protobuf:"bytes,1,opt,name=equipment_id,json=equipmentId,proto3" json:"equipment_id,omitempty"`
	EquipmentName     string                 `protobuf:"bytes,2,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"`
	Quantity          int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Index             int32                  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	MaximumStack      int32                  `protobuf:"varint,5,opt,name=maximum_stack,json=maximumStack,proto3" json:"maximum_stack,omitempty"`
	TargetEquipmentId int64                  `protobuf:"varint,6,opt,name=target_equipment_id,json=targetEquipmentId,proto3" json:"target_equipment_id,omitempty"`
	Price             int64                  `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`                                //给商店用的
	Contribution      int64                  `protobuf:"varint,8,opt,name=contribution,proto3" json:"contribution,omitempty"`                  //给商店用的
	SpecialCoin       int64                  `protobuf:"varint,9,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"` //给商店用的
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *EquipmentSlot) Reset() {
	*x = EquipmentSlot{}
	mi := &file_MainServer_Equipment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentSlot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentSlot) ProtoMessage() {}

func (x *EquipmentSlot) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentSlot.ProtoReflect.Descriptor instead.
func (*EquipmentSlot) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{0}
}

func (x *EquipmentSlot) GetEquipmentId() string {
	if x != nil {
		return x.EquipmentId
	}
	return ""
}

func (x *EquipmentSlot) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *EquipmentSlot) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *EquipmentSlot) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *EquipmentSlot) GetMaximumStack() int32 {
	if x != nil {
		return x.MaximumStack
	}
	return 0
}

func (x *EquipmentSlot) GetTargetEquipmentId() int64 {
	if x != nil {
		return x.TargetEquipmentId
	}
	return 0
}

func (x *EquipmentSlot) GetPrice() int64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *EquipmentSlot) GetContribution() int64 {
	if x != nil {
		return x.Contribution
	}
	return 0
}

func (x *EquipmentSlot) GetSpecialCoin() int64 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

type EquipmentList struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	EquipmentMap  map[string]*EquipmentConfigInfo `protobuf:"bytes,1,rep,name=equipment_map,json=equipmentMap,proto3" json:"equipment_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EquipmentList) Reset() {
	*x = EquipmentList{}
	mi := &file_MainServer_Equipment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentList) ProtoMessage() {}

func (x *EquipmentList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentList.ProtoReflect.Descriptor instead.
func (*EquipmentList) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{1}
}

func (x *EquipmentList) GetEquipmentMap() map[string]*EquipmentConfigInfo {
	if x != nil {
		return x.EquipmentMap
	}
	return nil
}

type FortifyMaterial struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	MaterialType     FortifyMaterialType    `protobuf:"varint,1,opt,name=material_type,json=materialType,proto3,enum=MainServer.FortifyMaterialType" json:"material_type,omitempty"`
	MaterialId       string                 `protobuf:"bytes,2,opt,name=material_id,json=materialId,proto3" json:"material_id,omitempty"`
	MaterialName     string                 `protobuf:"bytes,3,opt,name=material_name,json=materialName,proto3" json:"material_name,omitempty"`
	MaterialCount    int32                  `protobuf:"varint,4,opt,name=material_count,json=materialCount,proto3" json:"material_count,omitempty"`
	MaterialSubType  int32                  `protobuf:"varint,5,opt,name=material_sub_type,json=materialSubType,proto3" json:"material_sub_type,omitempty"`
	MaterialSubValue string                 `protobuf:"bytes,6,opt,name=material_sub_value,json=materialSubValue,proto3" json:"material_sub_value,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *FortifyMaterial) Reset() {
	*x = FortifyMaterial{}
	mi := &file_MainServer_Equipment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FortifyMaterial) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FortifyMaterial) ProtoMessage() {}

func (x *FortifyMaterial) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FortifyMaterial.ProtoReflect.Descriptor instead.
func (*FortifyMaterial) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{2}
}

func (x *FortifyMaterial) GetMaterialType() FortifyMaterialType {
	if x != nil {
		return x.MaterialType
	}
	return FortifyMaterialType_FortifyMaterialType_Unknown
}

func (x *FortifyMaterial) GetMaterialId() string {
	if x != nil {
		return x.MaterialId
	}
	return ""
}

func (x *FortifyMaterial) GetMaterialName() string {
	if x != nil {
		return x.MaterialName
	}
	return ""
}

func (x *FortifyMaterial) GetMaterialCount() int32 {
	if x != nil {
		return x.MaterialCount
	}
	return 0
}

func (x *FortifyMaterial) GetMaterialSubType() int32 {
	if x != nil {
		return x.MaterialSubType
	}
	return 0
}

func (x *FortifyMaterial) GetMaterialSubValue() string {
	if x != nil {
		return x.MaterialSubValue
	}
	return ""
}

type EquipmentConfigInfo struct {
	state            protoimpl.MessageState  `protogen:"open.v1"`
	EquipmentName    string                  `protobuf:"bytes,1,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"`
	TeamType         TrainerTeam             `protobuf:"varint,2,opt,name=team_type,json=teamType,proto3,enum=MainServer.TrainerTeam" json:"team_type,omitempty"`
	ItemSaleType     ItemSaleType            `protobuf:"varint,3,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.ItemSaleType" json:"item_sale_type,omitempty"`
	EquipmentType    EquipmentType           `protobuf:"varint,4,opt,name=equipment_type,json=equipmentType,proto3,enum=MainServer.EquipmentType" json:"equipment_type,omitempty"`
	ExpireTs         int64                   `protobuf:"varint,5,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	AllEquipments    []*EquipmentConfigValue `protobuf:"bytes,6,rep,name=all_equipments,json=allEquipments,proto3" json:"all_equipments,omitempty"`           // all组奖励
	OneEquipments    []*EquipmentConfigValue `protobuf:"bytes,7,rep,name=one_equipments,json=oneEquipments,proto3" json:"one_equipments,omitempty"`           // one组奖励
	TwoEquipments    []*EquipmentConfigValue `protobuf:"bytes,8,rep,name=two_equipments,json=twoEquipments,proto3" json:"two_equipments,omitempty"`           // two组奖励
	ThreeEquipments  []*EquipmentConfigValue `protobuf:"bytes,9,rep,name=three_equipments,json=threeEquipments,proto3" json:"three_equipments,omitempty"`     // three组奖励
	SpAllEquipments  []*EquipmentConfigValue `protobuf:"bytes,10,rep,name=sp_all_equipments,json=spAllEquipments,proto3" json:"sp_all_equipments,omitempty"`  //特别的all组奖励(比如徽章之类的只发一次)
	FortifyMaterials []*FortifyMaterial      `protobuf:"bytes,11,rep,name=fortify_materials,json=fortifyMaterials,proto3" json:"fortify_materials,omitempty"` //强化材料
	CanFortify       bool                    `protobuf:"varint,12,opt,name=canFortify,proto3" json:"canFortify,omitempty"`                                    //是否可以强化
	FortifyCoin      int32                   `protobuf:"varint,13,opt,name=fortify_coin,json=fortifyCoin,proto3" json:"fortify_coin,omitempty"`               //强化一次需要消耗的贡献值（每提升一级多1倍）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *EquipmentConfigInfo) Reset() {
	*x = EquipmentConfigInfo{}
	mi := &file_MainServer_Equipment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentConfigInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentConfigInfo) ProtoMessage() {}

func (x *EquipmentConfigInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentConfigInfo.ProtoReflect.Descriptor instead.
func (*EquipmentConfigInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{3}
}

func (x *EquipmentConfigInfo) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *EquipmentConfigInfo) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *EquipmentConfigInfo) GetItemSaleType() ItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return ItemSaleType_ItemSaleType_Normal
}

func (x *EquipmentConfigInfo) GetEquipmentType() EquipmentType {
	if x != nil {
		return x.EquipmentType
	}
	return EquipmentType_equipment_nor
}

func (x *EquipmentConfigInfo) GetExpireTs() int64 {
	if x != nil {
		return x.ExpireTs
	}
	return 0
}

func (x *EquipmentConfigInfo) GetAllEquipments() []*EquipmentConfigValue {
	if x != nil {
		return x.AllEquipments
	}
	return nil
}

func (x *EquipmentConfigInfo) GetOneEquipments() []*EquipmentConfigValue {
	if x != nil {
		return x.OneEquipments
	}
	return nil
}

func (x *EquipmentConfigInfo) GetTwoEquipments() []*EquipmentConfigValue {
	if x != nil {
		return x.TwoEquipments
	}
	return nil
}

func (x *EquipmentConfigInfo) GetThreeEquipments() []*EquipmentConfigValue {
	if x != nil {
		return x.ThreeEquipments
	}
	return nil
}

func (x *EquipmentConfigInfo) GetSpAllEquipments() []*EquipmentConfigValue {
	if x != nil {
		return x.SpAllEquipments
	}
	return nil
}

func (x *EquipmentConfigInfo) GetFortifyMaterials() []*FortifyMaterial {
	if x != nil {
		return x.FortifyMaterials
	}
	return nil
}

func (x *EquipmentConfigInfo) GetCanFortify() bool {
	if x != nil {
		return x.CanFortify
	}
	return false
}

func (x *EquipmentConfigInfo) GetFortifyCoin() int32 {
	if x != nil {
		return x.FortifyCoin
	}
	return 0
}

type EquipmentConfigValue struct {
	state               protoimpl.MessageState         `protogen:"open.v1"`
	EffectType          TrainerEquipmentEffectType     `protobuf:"varint,1,opt,name=effect_type,json=effectType,proto3,enum=MainServer.TrainerEquipmentEffectType" json:"effect_type,omitempty"`
	EffectTypeValue     float32                        `protobuf:"fixed32,2,opt,name=effect_type_value,json=effectTypeValue,proto3" json:"effect_type_value,omitempty"`
	EffectPokeType      TrainerEquipmentEffectPokeType `protobuf:"varint,3,opt,name=effect_poke_type,json=effectPokeType,proto3,enum=MainServer.TrainerEquipmentEffectPokeType" json:"effect_poke_type,omitempty"`
	EffectPokeTypeValue float32                        `protobuf:"fixed32,4,opt,name=effect_poke_type_value,json=effectPokeTypeValue,proto3" json:"effect_poke_type_value,omitempty"`
	DropRate            float32                        `protobuf:"fixed32,5,opt,name=drop_rate,json=dropRate,proto3" json:"drop_rate,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *EquipmentConfigValue) Reset() {
	*x = EquipmentConfigValue{}
	mi := &file_MainServer_Equipment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentConfigValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentConfigValue) ProtoMessage() {}

func (x *EquipmentConfigValue) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentConfigValue.ProtoReflect.Descriptor instead.
func (*EquipmentConfigValue) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{4}
}

func (x *EquipmentConfigValue) GetEffectType() TrainerEquipmentEffectType {
	if x != nil {
		return x.EffectType
	}
	return TrainerEquipmentEffectType_EquipmentEffect_None
}

func (x *EquipmentConfigValue) GetEffectTypeValue() float32 {
	if x != nil {
		return x.EffectTypeValue
	}
	return 0
}

func (x *EquipmentConfigValue) GetEffectPokeType() TrainerEquipmentEffectPokeType {
	if x != nil {
		return x.EffectPokeType
	}
	return TrainerEquipmentEffectPokeType_EffectPoke_Unknown
}

func (x *EquipmentConfigValue) GetEffectPokeTypeValue() float32 {
	if x != nil {
		return x.EffectPokeTypeValue
	}
	return 0
}

func (x *EquipmentConfigValue) GetDropRate() float32 {
	if x != nil {
		return x.DropRate
	}
	return 0
}

// 库存项目
type Equipment struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Id            int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                          // 库存ID
	Tid           int64                   `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                                        // 训练师ID
	EquipmentName string                  `protobuf:"bytes,3,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"`                                // 道具ID（道具名称）
	ExpiredTs     int64                   `protobuf:"varint,4,opt,name=expired_ts,json=expiredTs,proto3" json:"expired_ts,omitempty"`                                           // 过期时间戳（0为不过期
	Price         int32                   `protobuf:"varint,5,opt,name=price,proto3" json:"price,omitempty"`                                                                    // 价格
	TeamCoin      int32                   `protobuf:"varint,6,opt,name=team_coin,json=teamCoin,proto3" json:"team_coin,omitempty"`                                              // 团队贡献的价格
	TeamType      TrainerTeam             `protobuf:"varint,7,opt,name=team_type,json=teamType,proto3,enum=MainServer.TrainerTeam" json:"team_type,omitempty"`                  //团队类型 //比如召唤石是有团队类型的
	Status        EquipmentStatus         `protobuf:"varint,8,opt,name=status,proto3,enum=MainServer.EquipmentStatus" json:"status,omitempty"`                                  // 状态
	EquipmentType EquipmentType           `protobuf:"varint,9,opt,name=equipment_type,json=equipmentType,proto3,enum=MainServer.EquipmentType" json:"equipment_type,omitempty"` //道具类型(称号不能强化)
	ItemSaleType  ItemSaleType            `protobuf:"varint,10,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.ItemSaleType" json:"item_sale_type,omitempty"`  //表示是否是专属道具
	FortifyCount  int32                   `protobuf:"varint,11,opt,name=fortify_count,json=fortifyCount,proto3" json:"fortify_count,omitempty"`                                 //加强数 （+1）
	EffectInfo    *TrainerEquipmentEffect `protobuf:"bytes,12,opt,name=effect_info,json=effectInfo,proto3" json:"effect_info,omitempty"`                                        //效果信息
	CreateTs      int64                   `protobuf:"varint,13,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`                                             // 创建时间戳
	UpdateTs      int64                   `protobuf:"varint,14,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                             // 更新时间戳
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Equipment) Reset() {
	*x = Equipment{}
	mi := &file_MainServer_Equipment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Equipment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Equipment) ProtoMessage() {}

func (x *Equipment) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Equipment.ProtoReflect.Descriptor instead.
func (*Equipment) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{5}
}

func (x *Equipment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Equipment) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Equipment) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *Equipment) GetExpiredTs() int64 {
	if x != nil {
		return x.ExpiredTs
	}
	return 0
}

func (x *Equipment) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *Equipment) GetTeamCoin() int32 {
	if x != nil {
		return x.TeamCoin
	}
	return 0
}

func (x *Equipment) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

func (x *Equipment) GetStatus() EquipmentStatus {
	if x != nil {
		return x.Status
	}
	return EquipmentStatus_EquipmentStatus_Unknown
}

func (x *Equipment) GetEquipmentType() EquipmentType {
	if x != nil {
		return x.EquipmentType
	}
	return EquipmentType_equipment_nor
}

func (x *Equipment) GetItemSaleType() ItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return ItemSaleType_ItemSaleType_Normal
}

func (x *Equipment) GetFortifyCount() int32 {
	if x != nil {
		return x.FortifyCount
	}
	return 0
}

func (x *Equipment) GetEffectInfo() *TrainerEquipmentEffect {
	if x != nil {
		return x.EffectInfo
	}
	return nil
}

func (x *Equipment) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Equipment) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type TrainerEquipmentEffectPokeAllOneTypeInfo struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	EffectPokeNormal   float32                `protobuf:"fixed32,1,opt,name=effectPoke_normal,json=effectPokeNormal,proto3" json:"effectPoke_normal,omitempty"`
	EffectPokeFire     float32                `protobuf:"fixed32,2,opt,name=effectPoke_fire,json=effectPokeFire,proto3" json:"effectPoke_fire,omitempty"`
	EffectPokeWater    float32                `protobuf:"fixed32,3,opt,name=effectPoke_water,json=effectPokeWater,proto3" json:"effectPoke_water,omitempty"`
	EffectPokeElectric float32                `protobuf:"fixed32,4,opt,name=effectPoke_electric,json=effectPokeElectric,proto3" json:"effectPoke_electric,omitempty"`
	EffectPokeGrass    float32                `protobuf:"fixed32,5,opt,name=effectPoke_grass,json=effectPokeGrass,proto3" json:"effectPoke_grass,omitempty"`
	EffectPokeIce      float32                `protobuf:"fixed32,6,opt,name=effectPoke_ice,json=effectPokeIce,proto3" json:"effectPoke_ice,omitempty"`
	EffectPokeFighting float32                `protobuf:"fixed32,7,opt,name=effectPoke_fighting,json=effectPokeFighting,proto3" json:"effectPoke_fighting,omitempty"`
	EffectPokePoison   float32                `protobuf:"fixed32,8,opt,name=effectPoke_poison,json=effectPokePoison,proto3" json:"effectPoke_poison,omitempty"`
	EffectPokeGround   float32                `protobuf:"fixed32,9,opt,name=effectPoke_ground,json=effectPokeGround,proto3" json:"effectPoke_ground,omitempty"`
	EffectPokeFlying   float32                `protobuf:"fixed32,10,opt,name=effectPoke_flying,json=effectPokeFlying,proto3" json:"effectPoke_flying,omitempty"`
	EffectPokePsychic  float32                `protobuf:"fixed32,11,opt,name=effectPoke_psychic,json=effectPokePsychic,proto3" json:"effectPoke_psychic,omitempty"`
	EffectPokeBug      float32                `protobuf:"fixed32,12,opt,name=effectPoke_bug,json=effectPokeBug,proto3" json:"effectPoke_bug,omitempty"`
	EffectPokeRock     float32                `protobuf:"fixed32,13,opt,name=effectPoke_rock,json=effectPokeRock,proto3" json:"effectPoke_rock,omitempty"`
	EffectPokeGhost    float32                `protobuf:"fixed32,14,opt,name=effectPoke_ghost,json=effectPokeGhost,proto3" json:"effectPoke_ghost,omitempty"`
	EffectPokeDragon   float32                `protobuf:"fixed32,15,opt,name=effectPoke_dragon,json=effectPokeDragon,proto3" json:"effectPoke_dragon,omitempty"`
	EffectPokeDark     float32                `protobuf:"fixed32,16,opt,name=effectPoke_dark,json=effectPokeDark,proto3" json:"effectPoke_dark,omitempty"`
	EffectPokeSteel    float32                `protobuf:"fixed32,17,opt,name=effectPoke_steel,json=effectPokeSteel,proto3" json:"effectPoke_steel,omitempty"`
	EffectPokeFairy    float32                `protobuf:"fixed32,18,opt,name=effectPoke_fairy,json=effectPokeFairy,proto3" json:"effectPoke_fairy,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) Reset() {
	*x = TrainerEquipmentEffectPokeAllOneTypeInfo{}
	mi := &file_MainServer_Equipment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerEquipmentEffectPokeAllOneTypeInfo) ProtoMessage() {}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerEquipmentEffectPokeAllOneTypeInfo.ProtoReflect.Descriptor instead.
func (*TrainerEquipmentEffectPokeAllOneTypeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{6}
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeNormal() float32 {
	if x != nil {
		return x.EffectPokeNormal
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeFire() float32 {
	if x != nil {
		return x.EffectPokeFire
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeWater() float32 {
	if x != nil {
		return x.EffectPokeWater
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeElectric() float32 {
	if x != nil {
		return x.EffectPokeElectric
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeGrass() float32 {
	if x != nil {
		return x.EffectPokeGrass
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeIce() float32 {
	if x != nil {
		return x.EffectPokeIce
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeFighting() float32 {
	if x != nil {
		return x.EffectPokeFighting
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokePoison() float32 {
	if x != nil {
		return x.EffectPokePoison
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeGround() float32 {
	if x != nil {
		return x.EffectPokeGround
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeFlying() float32 {
	if x != nil {
		return x.EffectPokeFlying
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokePsychic() float32 {
	if x != nil {
		return x.EffectPokePsychic
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeBug() float32 {
	if x != nil {
		return x.EffectPokeBug
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeRock() float32 {
	if x != nil {
		return x.EffectPokeRock
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeGhost() float32 {
	if x != nil {
		return x.EffectPokeGhost
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeDragon() float32 {
	if x != nil {
		return x.EffectPokeDragon
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeDark() float32 {
	if x != nil {
		return x.EffectPokeDark
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeSteel() float32 {
	if x != nil {
		return x.EffectPokeSteel
	}
	return 0
}

func (x *TrainerEquipmentEffectPokeAllOneTypeInfo) GetEffectPokeFairy() float32 {
	if x != nil {
		return x.EffectPokeFairy
	}
	return 0
}

type TrainerEquipmentEffect struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	EquipmentEffect     map[int32]float32      `protobuf:"bytes,1,rep,name=equipmentEffect,proto3" json:"equipmentEffect,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"`         //TrainerEquipmentEffectType -> float
	EquipmentPokeEffect map[int32]float32      `protobuf:"bytes,2,rep,name=equipmentPokeEffect,proto3" json:"equipmentPokeEffect,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"` //TrainerEquipmentEffectPokeType -> float
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *TrainerEquipmentEffect) Reset() {
	*x = TrainerEquipmentEffect{}
	mi := &file_MainServer_Equipment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerEquipmentEffect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerEquipmentEffect) ProtoMessage() {}

func (x *TrainerEquipmentEffect) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerEquipmentEffect.ProtoReflect.Descriptor instead.
func (*TrainerEquipmentEffect) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{7}
}

func (x *TrainerEquipmentEffect) GetEquipmentEffect() map[int32]float32 {
	if x != nil {
		return x.EquipmentEffect
	}
	return nil
}

func (x *TrainerEquipmentEffect) GetEquipmentPokeEffect() map[int32]float32 {
	if x != nil {
		return x.EquipmentPokeEffect
	}
	return nil
}

type TrainerEquipmentAllOneEffect struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 野外遇敌
	EncounterEnemy         float32 `protobuf:"fixed32,1,opt,name=encounterEnemy,proto3" json:"encounterEnemy,omitempty"`                 //遇敌概率 （这个还是不要了，或者单独给一个）
	EncounterShine         float32 `protobuf:"fixed32,2,opt,name=encounterShine,proto3" json:"encounterShine,omitempty"`                 //遇闪概率
	EncounterGoodEnemy     float32 `protobuf:"fixed32,3,opt,name=encounterGoodEnemy,proto3" json:"encounterGoodEnemy,omitempty"`         //遇高v poke的概率
	GetBattlePoke          float32 `protobuf:"fixed32,4,opt,name=getBattlePoke,proto3" json:"getBattlePoke,omitempty"`                   //捕获poke的概率
	GetWildBattleEquipment float32 `protobuf:"fixed32,5,opt,name=getWildBattleEquipment,proto3" json:"getWildBattleEquipment,omitempty"` //野外战斗道具的概率
	FishingGoodEquipment   float32 `protobuf:"fixed32,6,opt,name=fishingGoodEquipment,proto3" json:"fishingGoodEquipment,omitempty"`     //钓鱼钓到好道具（金钱）的概率
	FishingBattleEquipment float32 `protobuf:"fixed32,7,opt,name=fishingBattleEquipment,proto3" json:"fishingBattleEquipment,omitempty"` //钓到战斗道具的概率
	// 战斗
	GetBattleExp   float32 `protobuf:"fixed32,101,opt,name=getBattleExp,proto3" json:"getBattleExp,omitempty"`     //获得战斗经验的概率
	GetBattleMoney float32 `protobuf:"fixed32,102,opt,name=getBattleMoney,proto3" json:"getBattleMoney,omitempty"` //获得战斗金钱的概率
	// 孵蛋
	BreedGoodPokebaby  float32 `protobuf:"fixed32,201,opt,name=breedGoodPokebaby,proto3" json:"breedGoodPokebaby,omitempty"`   //生育好宝宝的概率
	BreedDifficultBaby float32 `protobuf:"fixed32,202,opt,name=breedDifficultBaby,proto3" json:"breedDifficultBaby,omitempty"` //父母难产的概率
	FishingGoodPoke    float32 `protobuf:"fixed32,203,opt,name=fishingGoodPoke,proto3" json:"fishingGoodPoke,omitempty"`       //钓鱼钓到好poke的概率
	GetBattleEvs       float32 `protobuf:"fixed32,204,opt,name=getBattleEvs,proto3" json:"getBattleEvs,omitempty"`             //获得战斗evs的概率
	HatchEggTime       float32 `protobuf:"fixed32,205,opt,name=hatchEggTime,proto3" json:"hatchEggTime,omitempty"`             //孵蛋时间
	BreedShine         float32 `protobuf:"fixed32,206,opt,name=breedShine,proto3" json:"breedShine,omitempty"`                 //孵蛋遇闪概率
	// 家园
	TreeFruitGrowup   float32 `protobuf:"fixed32,301,opt,name=treeFruitGrowup,proto3" json:"treeFruitGrowup,omitempty"`     //树果成长速度
	TreeFruitWithered float32 `protobuf:"fixed32,302,opt,name=treeFruitWithered,proto3" json:"treeFruitWithered,omitempty"` //树果枯萎速度概率
	// 其他
	BorrowGoodPoke float32 `protobuf:"fixed32,901,opt,name=borrowGoodPoke,proto3" json:"borrowGoodPoke,omitempty"` //借到好poke的概率
	TeamBind       float32 `protobuf:"fixed32,902,opt,name=teamBind,proto3" json:"teamBind,omitempty"`             //绑定团队(会和团队的部分效果相关（比如特定的精灵（不单独生效需要其他效果辅助生效)
	// poke类型效果
	EffectPokeTypeInfo *TrainerEquipmentEffectPokeAllOneTypeInfo `protobuf:"bytes,1000,opt,name=effectPokeTypeInfo,proto3" json:"effectPokeTypeInfo,omitempty"` //poke类型效果
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *TrainerEquipmentAllOneEffect) Reset() {
	*x = TrainerEquipmentAllOneEffect{}
	mi := &file_MainServer_Equipment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrainerEquipmentAllOneEffect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrainerEquipmentAllOneEffect) ProtoMessage() {}

func (x *TrainerEquipmentAllOneEffect) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrainerEquipmentAllOneEffect.ProtoReflect.Descriptor instead.
func (*TrainerEquipmentAllOneEffect) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{8}
}

func (x *TrainerEquipmentAllOneEffect) GetEncounterEnemy() float32 {
	if x != nil {
		return x.EncounterEnemy
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetEncounterShine() float32 {
	if x != nil {
		return x.EncounterShine
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetEncounterGoodEnemy() float32 {
	if x != nil {
		return x.EncounterGoodEnemy
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetGetBattlePoke() float32 {
	if x != nil {
		return x.GetBattlePoke
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetGetWildBattleEquipment() float32 {
	if x != nil {
		return x.GetWildBattleEquipment
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetFishingGoodEquipment() float32 {
	if x != nil {
		return x.FishingGoodEquipment
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetFishingBattleEquipment() float32 {
	if x != nil {
		return x.FishingBattleEquipment
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetGetBattleExp() float32 {
	if x != nil {
		return x.GetBattleExp
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetGetBattleMoney() float32 {
	if x != nil {
		return x.GetBattleMoney
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetBreedGoodPokebaby() float32 {
	if x != nil {
		return x.BreedGoodPokebaby
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetBreedDifficultBaby() float32 {
	if x != nil {
		return x.BreedDifficultBaby
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetFishingGoodPoke() float32 {
	if x != nil {
		return x.FishingGoodPoke
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetGetBattleEvs() float32 {
	if x != nil {
		return x.GetBattleEvs
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetHatchEggTime() float32 {
	if x != nil {
		return x.HatchEggTime
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetBreedShine() float32 {
	if x != nil {
		return x.BreedShine
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetTreeFruitGrowup() float32 {
	if x != nil {
		return x.TreeFruitGrowup
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetTreeFruitWithered() float32 {
	if x != nil {
		return x.TreeFruitWithered
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetBorrowGoodPoke() float32 {
	if x != nil {
		return x.BorrowGoodPoke
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetTeamBind() float32 {
	if x != nil {
		return x.TeamBind
	}
	return 0
}

func (x *TrainerEquipmentAllOneEffect) GetEffectPokeTypeInfo() *TrainerEquipmentEffectPokeAllOneTypeInfo {
	if x != nil {
		return x.EffectPokeTypeInfo
	}
	return nil
}

type EquipmentFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                                        // 训练师ID（必填）
	EquipmentName string                 `protobuf:"bytes,3,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"`                                // 道具名称（可选）
	Status        EquipmentStatus        `protobuf:"varint,4,opt,name=status,proto3,enum=MainServer.EquipmentStatus" json:"status,omitempty"`                                  // 道具状态（可选）、
	FortifyCount  int32                  `protobuf:"varint,5,opt,name=fortify_count,json=fortifyCount,proto3" json:"fortify_count,omitempty"`                                  //加强数 （+1）
	ItemSaleType  ItemSaleType           `protobuf:"varint,6,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.ItemSaleType" json:"item_sale_type,omitempty"`   //表示是否是专属道具
	UpdateTs      int64                  `protobuf:"varint,8,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                              // 更新时间戳（可选）
	EquipmentType EquipmentType          `protobuf:"varint,9,opt,name=equipment_type,json=equipmentType,proto3,enum=MainServer.EquipmentType" json:"equipment_type,omitempty"` //道具类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EquipmentFilter) Reset() {
	*x = EquipmentFilter{}
	mi := &file_MainServer_Equipment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipmentFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipmentFilter) ProtoMessage() {}

func (x *EquipmentFilter) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipmentFilter.ProtoReflect.Descriptor instead.
func (*EquipmentFilter) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{9}
}

func (x *EquipmentFilter) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EquipmentFilter) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *EquipmentFilter) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *EquipmentFilter) GetStatus() EquipmentStatus {
	if x != nil {
		return x.Status
	}
	return EquipmentStatus_EquipmentStatus_Unknown
}

func (x *EquipmentFilter) GetFortifyCount() int32 {
	if x != nil {
		return x.FortifyCount
	}
	return 0
}

func (x *EquipmentFilter) GetItemSaleType() ItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return ItemSaleType_ItemSaleType_Normal
}

func (x *EquipmentFilter) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *EquipmentFilter) GetEquipmentType() EquipmentType {
	if x != nil {
		return x.EquipmentType
	}
	return EquipmentType_equipment_nor
}

// 上架道具请求参数
type SaleEquipmentParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EquipmentName string                 `protobuf:"bytes,1,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"` // 道具ID（道具名称）
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                     // 上架数量
	Price         int32                  `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`                                     // 价格
	SpecialCoin   int32                  `protobuf:"varint,4,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`      // 特殊货币
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaleEquipmentParam) Reset() {
	*x = SaleEquipmentParam{}
	mi := &file_MainServer_Equipment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaleEquipmentParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleEquipmentParam) ProtoMessage() {}

func (x *SaleEquipmentParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleEquipmentParam.ProtoReflect.Descriptor instead.
func (*SaleEquipmentParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{10}
}

func (x *SaleEquipmentParam) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *SaleEquipmentParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SaleEquipmentParam) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SaleEquipmentParam) GetSpecialCoin() int32 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

// 下架道具请求参数
type UnsaleEquipmentParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EquipmentName string                 `protobuf:"bytes,1,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"` // 道具ID（道具名称）
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                     // 下架数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsaleEquipmentParam) Reset() {
	*x = UnsaleEquipmentParam{}
	mi := &file_MainServer_Equipment_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsaleEquipmentParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsaleEquipmentParam) ProtoMessage() {}

func (x *UnsaleEquipmentParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsaleEquipmentParam.ProtoReflect.Descriptor instead.
func (*UnsaleEquipmentParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{11}
}

func (x *UnsaleEquipmentParam) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *UnsaleEquipmentParam) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 查询道具请求参数
type QueryEquipmentsParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EquipmentName string                 `protobuf:"bytes,1,opt,name=equipment_name,json=equipmentName,proto3" json:"equipment_name,omitempty"`                                // 道具ID（道具名称），空字符串表示查询所有道具
	Status        EquipmentStatus        `protobuf:"varint,2,opt,name=status,proto3,enum=MainServer.EquipmentStatus" json:"status,omitempty"`                                  // 状态，0表示查询所有状态
	UpdateTs      int64                  `protobuf:"varint,3,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`                                              // 更新时间戳，大于0时只查询更新时间大于此值的记录
	EquipmentType EquipmentType          `protobuf:"varint,4,opt,name=equipment_type,json=equipmentType,proto3,enum=MainServer.EquipmentType" json:"equipment_type,omitempty"` //道具类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryEquipmentsParam) Reset() {
	*x = QueryEquipmentsParam{}
	mi := &file_MainServer_Equipment_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryEquipmentsParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEquipmentsParam) ProtoMessage() {}

func (x *QueryEquipmentsParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEquipmentsParam.ProtoReflect.Descriptor instead.
func (*QueryEquipmentsParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{12}
}

func (x *QueryEquipmentsParam) GetEquipmentName() string {
	if x != nil {
		return x.EquipmentName
	}
	return ""
}

func (x *QueryEquipmentsParam) GetStatus() EquipmentStatus {
	if x != nil {
		return x.Status
	}
	return EquipmentStatus_EquipmentStatus_Unknown
}

func (x *QueryEquipmentsParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *QueryEquipmentsParam) GetEquipmentType() EquipmentType {
	if x != nil {
		return x.EquipmentType
	}
	return EquipmentType_equipment_nor
}

type EquipToDecorationParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EquipmentId   int64                  `protobuf:"varint,1,opt,name=equipment_id,json=equipmentId,proto3" json:"equipment_id,omitempty"`                                     // 道具ID
	EquipmentType EquipmentType          `protobuf:"varint,2,opt,name=equipment_type,json=equipmentType,proto3,enum=MainServer.EquipmentType" json:"equipment_type,omitempty"` //道具类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EquipToDecorationParam) Reset() {
	*x = EquipToDecorationParam{}
	mi := &file_MainServer_Equipment_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EquipToDecorationParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquipToDecorationParam) ProtoMessage() {}

func (x *EquipToDecorationParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquipToDecorationParam.ProtoReflect.Descriptor instead.
func (*EquipToDecorationParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{13}
}

func (x *EquipToDecorationParam) GetEquipmentId() int64 {
	if x != nil {
		return x.EquipmentId
	}
	return 0
}

func (x *EquipToDecorationParam) GetEquipmentType() EquipmentType {
	if x != nil {
		return x.EquipmentType
	}
	return EquipmentType_equipment_nor
}

type UnequipToDecorationParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EquipmentType EquipmentType          `protobuf:"varint,1,opt,name=equipment_type,json=equipmentType,proto3,enum=MainServer.EquipmentType" json:"equipment_type,omitempty"` //道具类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnequipToDecorationParam) Reset() {
	*x = UnequipToDecorationParam{}
	mi := &file_MainServer_Equipment_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnequipToDecorationParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnequipToDecorationParam) ProtoMessage() {}

func (x *UnequipToDecorationParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnequipToDecorationParam.ProtoReflect.Descriptor instead.
func (*UnequipToDecorationParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{14}
}

func (x *UnequipToDecorationParam) GetEquipmentType() EquipmentType {
	if x != nil {
		return x.EquipmentType
	}
	return EquipmentType_equipment_nor
}

// 添加道具响应
type AddEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddEquipmentResponse) Reset() {
	*x = AddEquipmentResponse{}
	mi := &file_MainServer_Equipment_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddEquipmentResponse) ProtoMessage() {}

func (x *AddEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddEquipmentResponse.ProtoReflect.Descriptor instead.
func (*AddEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{15}
}

func (x *AddEquipmentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *AddEquipmentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 上架道具响应
type SaleEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaleEquipmentResponse) Reset() {
	*x = SaleEquipmentResponse{}
	mi := &file_MainServer_Equipment_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaleEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleEquipmentResponse) ProtoMessage() {}

func (x *SaleEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleEquipmentResponse.ProtoReflect.Descriptor instead.
func (*SaleEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{16}
}

func (x *SaleEquipmentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SaleEquipmentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 下架道具响应
type UnsaleEquipmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnsaleEquipmentResponse) Reset() {
	*x = UnsaleEquipmentResponse{}
	mi := &file_MainServer_Equipment_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnsaleEquipmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnsaleEquipmentResponse) ProtoMessage() {}

func (x *UnsaleEquipmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnsaleEquipmentResponse.ProtoReflect.Descriptor instead.
func (*UnsaleEquipmentResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{17}
}

func (x *UnsaleEquipmentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UnsaleEquipmentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取所有道具请求参数
type GetAllEquipmentsParam struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UpdateTs      int64                  `protobuf:"varint,1,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳，大于0时只查询更新时间大于此值的记录
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllEquipmentsParam) Reset() {
	*x = GetAllEquipmentsParam{}
	mi := &file_MainServer_Equipment_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEquipmentsParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEquipmentsParam) ProtoMessage() {}

func (x *GetAllEquipmentsParam) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEquipmentsParam.ProtoReflect.Descriptor instead.
func (*GetAllEquipmentsParam) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{18}
}

func (x *GetAllEquipmentsParam) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

// 获取所有道具响应
type GetAllEquipmentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`      // 是否成功
	Equipments    []*Equipment           `protobuf:"bytes,2,rep,name=equipments,proto3" json:"equipments,omitempty"` // 道具列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllEquipmentsResponse) Reset() {
	*x = GetAllEquipmentsResponse{}
	mi := &file_MainServer_Equipment_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllEquipmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllEquipmentsResponse) ProtoMessage() {}

func (x *GetAllEquipmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllEquipmentsResponse.ProtoReflect.Descriptor instead.
func (*GetAllEquipmentsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{19}
}

func (x *GetAllEquipmentsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *GetAllEquipmentsResponse) GetEquipments() []*Equipment {
	if x != nil {
		return x.Equipments
	}
	return nil
}

// 查询道具响应
type QueryEquipmentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`      // 是否成功
	Equipments    []*Equipment           `protobuf:"bytes,2,rep,name=equipments,proto3" json:"equipments,omitempty"` // 道具列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryEquipmentsResponse) Reset() {
	*x = QueryEquipmentsResponse{}
	mi := &file_MainServer_Equipment_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryEquipmentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEquipmentsResponse) ProtoMessage() {}

func (x *QueryEquipmentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Equipment_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEquipmentsResponse.ProtoReflect.Descriptor instead.
func (*QueryEquipmentsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Equipment_proto_rawDescGZIP(), []int{20}
}

func (x *QueryEquipmentsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *QueryEquipmentsResponse) GetEquipments() []*Equipment {
	if x != nil {
		return x.Equipments
	}
	return nil
}

var File_MainServer_Equipment_proto protoreflect.FileDescriptor

const file_MainServer_Equipment_proto_rawDesc = "" +
	"\n" +
	"\x1aMainServer/Equipment.proto\x12\n" +
	"MainServer\x1a\x1cMainServer/TrainerTeam.proto\x1a\x1aMainServer/Inventory.proto\"\xbd\x02\n" +
	"\rEquipmentSlot\x12!\n" +
	"\fequipment_id\x18\x01 \x01(\tR\vequipmentId\x12%\n" +
	"\x0eequipment_name\x18\x02 \x01(\tR\requipmentName\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05index\x18\x04 \x01(\x05R\x05index\x12#\n" +
	"\rmaximum_stack\x18\x05 \x01(\x05R\fmaximumStack\x12.\n" +
	"\x13target_equipment_id\x18\x06 \x01(\x03R\x11targetEquipmentId\x12\x14\n" +
	"\x05price\x18\a \x01(\x03R\x05price\x12\"\n" +
	"\fcontribution\x18\b \x01(\x03R\fcontribution\x12!\n" +
	"\fspecial_coin\x18\t \x01(\x03R\vspecialCoin\"\xc3\x01\n" +
	"\rEquipmentList\x12P\n" +
	"\requipment_map\x18\x01 \x03(\v2+.MainServer.EquipmentList.EquipmentMapEntryR\fequipmentMap\x1a`\n" +
	"\x11EquipmentMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x125\n" +
	"\x05value\x18\x02 \x01(\v2\x1f.MainServer.EquipmentConfigInfoR\x05value:\x028\x01\"\x9e\x02\n" +
	"\x0fFortifyMaterial\x12D\n" +
	"\rmaterial_type\x18\x01 \x01(\x0e2\x1f.MainServer.FortifyMaterialTypeR\fmaterialType\x12\x1f\n" +
	"\vmaterial_id\x18\x02 \x01(\tR\n" +
	"materialId\x12#\n" +
	"\rmaterial_name\x18\x03 \x01(\tR\fmaterialName\x12%\n" +
	"\x0ematerial_count\x18\x04 \x01(\x05R\rmaterialCount\x12*\n" +
	"\x11material_sub_type\x18\x05 \x01(\x05R\x0fmaterialSubType\x12,\n" +
	"\x12material_sub_value\x18\x06 \x01(\tR\x10materialSubValue\"\x94\x06\n" +
	"\x13EquipmentConfigInfo\x12%\n" +
	"\x0eequipment_name\x18\x01 \x01(\tR\requipmentName\x124\n" +
	"\tteam_type\x18\x02 \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\x12>\n" +
	"\x0eitem_sale_type\x18\x03 \x01(\x0e2\x18.MainServer.ItemSaleTypeR\fitemSaleType\x12@\n" +
	"\x0eequipment_type\x18\x04 \x01(\x0e2\x19.MainServer.EquipmentTypeR\requipmentType\x12\x1b\n" +
	"\texpire_ts\x18\x05 \x01(\x03R\bexpireTs\x12G\n" +
	"\x0eall_equipments\x18\x06 \x03(\v2 .MainServer.EquipmentConfigValueR\rallEquipments\x12G\n" +
	"\x0eone_equipments\x18\a \x03(\v2 .MainServer.EquipmentConfigValueR\roneEquipments\x12G\n" +
	"\x0etwo_equipments\x18\b \x03(\v2 .MainServer.EquipmentConfigValueR\rtwoEquipments\x12K\n" +
	"\x10three_equipments\x18\t \x03(\v2 .MainServer.EquipmentConfigValueR\x0fthreeEquipments\x12L\n" +
	"\x11sp_all_equipments\x18\n" +
	" \x03(\v2 .MainServer.EquipmentConfigValueR\x0fspAllEquipments\x12H\n" +
	"\x11fortify_materials\x18\v \x03(\v2\x1b.MainServer.FortifyMaterialR\x10fortifyMaterials\x12\x1e\n" +
	"\n" +
	"canFortify\x18\f \x01(\bR\n" +
	"canFortify\x12!\n" +
	"\ffortify_coin\x18\r \x01(\x05R\vfortifyCoin\"\xb3\x02\n" +
	"\x14EquipmentConfigValue\x12G\n" +
	"\veffect_type\x18\x01 \x01(\x0e2&.MainServer.TrainerEquipmentEffectTypeR\n" +
	"effectType\x12*\n" +
	"\x11effect_type_value\x18\x02 \x01(\x02R\x0feffectTypeValue\x12T\n" +
	"\x10effect_poke_type\x18\x03 \x01(\x0e2*.MainServer.TrainerEquipmentEffectPokeTypeR\x0eeffectPokeType\x123\n" +
	"\x16effect_poke_type_value\x18\x04 \x01(\x02R\x13effectPokeTypeValue\x12\x1b\n" +
	"\tdrop_rate\x18\x05 \x01(\x02R\bdropRate\"\xb7\x04\n" +
	"\tEquipment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12%\n" +
	"\x0eequipment_name\x18\x03 \x01(\tR\requipmentName\x12\x1d\n" +
	"\n" +
	"expired_ts\x18\x04 \x01(\x03R\texpiredTs\x12\x14\n" +
	"\x05price\x18\x05 \x01(\x05R\x05price\x12\x1b\n" +
	"\tteam_coin\x18\x06 \x01(\x05R\bteamCoin\x124\n" +
	"\tteam_type\x18\a \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\x123\n" +
	"\x06status\x18\b \x01(\x0e2\x1b.MainServer.EquipmentStatusR\x06status\x12@\n" +
	"\x0eequipment_type\x18\t \x01(\x0e2\x19.MainServer.EquipmentTypeR\requipmentType\x12>\n" +
	"\x0eitem_sale_type\x18\n" +
	" \x01(\x0e2\x18.MainServer.ItemSaleTypeR\fitemSaleType\x12#\n" +
	"\rfortify_count\x18\v \x01(\x05R\ffortifyCount\x12C\n" +
	"\veffect_info\x18\f \x01(\v2\".MainServer.TrainerEquipmentEffectR\n" +
	"effectInfo\x12\x1b\n" +
	"\tcreate_ts\x18\r \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x0e \x01(\x03R\bupdateTs\"\xbc\x06\n" +
	"(TrainerEquipmentEffectPokeAllOneTypeInfo\x12+\n" +
	"\x11effectPoke_normal\x18\x01 \x01(\x02R\x10effectPokeNormal\x12'\n" +
	"\x0feffectPoke_fire\x18\x02 \x01(\x02R\x0eeffectPokeFire\x12)\n" +
	"\x10effectPoke_water\x18\x03 \x01(\x02R\x0feffectPokeWater\x12/\n" +
	"\x13effectPoke_electric\x18\x04 \x01(\x02R\x12effectPokeElectric\x12)\n" +
	"\x10effectPoke_grass\x18\x05 \x01(\x02R\x0feffectPokeGrass\x12%\n" +
	"\x0eeffectPoke_ice\x18\x06 \x01(\x02R\reffectPokeIce\x12/\n" +
	"\x13effectPoke_fighting\x18\a \x01(\x02R\x12effectPokeFighting\x12+\n" +
	"\x11effectPoke_poison\x18\b \x01(\x02R\x10effectPokePoison\x12+\n" +
	"\x11effectPoke_ground\x18\t \x01(\x02R\x10effectPokeGround\x12+\n" +
	"\x11effectPoke_flying\x18\n" +
	" \x01(\x02R\x10effectPokeFlying\x12-\n" +
	"\x12effectPoke_psychic\x18\v \x01(\x02R\x11effectPokePsychic\x12%\n" +
	"\x0eeffectPoke_bug\x18\f \x01(\x02R\reffectPokeBug\x12'\n" +
	"\x0feffectPoke_rock\x18\r \x01(\x02R\x0eeffectPokeRock\x12)\n" +
	"\x10effectPoke_ghost\x18\x0e \x01(\x02R\x0feffectPokeGhost\x12+\n" +
	"\x11effectPoke_dragon\x18\x0f \x01(\x02R\x10effectPokeDragon\x12'\n" +
	"\x0feffectPoke_dark\x18\x10 \x01(\x02R\x0eeffectPokeDark\x12)\n" +
	"\x10effectPoke_steel\x18\x11 \x01(\x02R\x0feffectPokeSteel\x12)\n" +
	"\x10effectPoke_fairy\x18\x12 \x01(\x02R\x0feffectPokeFairy\"\xf6\x02\n" +
	"\x16TrainerEquipmentEffect\x12a\n" +
	"\x0fequipmentEffect\x18\x01 \x03(\v27.MainServer.TrainerEquipmentEffect.EquipmentEffectEntryR\x0fequipmentEffect\x12m\n" +
	"\x13equipmentPokeEffect\x18\x02 \x03(\v2;.MainServer.TrainerEquipmentEffect.EquipmentPokeEffectEntryR\x13equipmentPokeEffect\x1aB\n" +
	"\x14EquipmentEffectEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x02R\x05value:\x028\x01\x1aF\n" +
	"\x18EquipmentPokeEffectEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x02R\x05value:\x028\x01\"\xb1\a\n" +
	"\x1cTrainerEquipmentAllOneEffect\x12&\n" +
	"\x0eencounterEnemy\x18\x01 \x01(\x02R\x0eencounterEnemy\x12&\n" +
	"\x0eencounterShine\x18\x02 \x01(\x02R\x0eencounterShine\x12.\n" +
	"\x12encounterGoodEnemy\x18\x03 \x01(\x02R\x12encounterGoodEnemy\x12$\n" +
	"\rgetBattlePoke\x18\x04 \x01(\x02R\rgetBattlePoke\x126\n" +
	"\x16getWildBattleEquipment\x18\x05 \x01(\x02R\x16getWildBattleEquipment\x122\n" +
	"\x14fishingGoodEquipment\x18\x06 \x01(\x02R\x14fishingGoodEquipment\x126\n" +
	"\x16fishingBattleEquipment\x18\a \x01(\x02R\x16fishingBattleEquipment\x12\"\n" +
	"\fgetBattleExp\x18e \x01(\x02R\fgetBattleExp\x12&\n" +
	"\x0egetBattleMoney\x18f \x01(\x02R\x0egetBattleMoney\x12-\n" +
	"\x11breedGoodPokebaby\x18\xc9\x01 \x01(\x02R\x11breedGoodPokebaby\x12/\n" +
	"\x12breedDifficultBaby\x18\xca\x01 \x01(\x02R\x12breedDifficultBaby\x12)\n" +
	"\x0ffishingGoodPoke\x18\xcb\x01 \x01(\x02R\x0ffishingGoodPoke\x12#\n" +
	"\fgetBattleEvs\x18\xcc\x01 \x01(\x02R\fgetBattleEvs\x12#\n" +
	"\fhatchEggTime\x18\xcd\x01 \x01(\x02R\fhatchEggTime\x12\x1f\n" +
	"\n" +
	"breedShine\x18\xce\x01 \x01(\x02R\n" +
	"breedShine\x12)\n" +
	"\x0ftreeFruitGrowup\x18\xad\x02 \x01(\x02R\x0ftreeFruitGrowup\x12-\n" +
	"\x11treeFruitWithered\x18\xae\x02 \x01(\x02R\x11treeFruitWithered\x12'\n" +
	"\x0eborrowGoodPoke\x18\x85\a \x01(\x02R\x0eborrowGoodPoke\x12\x1b\n" +
	"\bteamBind\x18\x86\a \x01(\x02R\bteamBind\x12e\n" +
	"\x12effectPokeTypeInfo\x18\xe8\a \x01(\v24.MainServer.TrainerEquipmentEffectPokeAllOneTypeInfoR\x12effectPokeTypeInfo\"\xd3\x02\n" +
	"\x0fEquipmentFilter\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12%\n" +
	"\x0eequipment_name\x18\x03 \x01(\tR\requipmentName\x123\n" +
	"\x06status\x18\x04 \x01(\x0e2\x1b.MainServer.EquipmentStatusR\x06status\x12#\n" +
	"\rfortify_count\x18\x05 \x01(\x05R\ffortifyCount\x12>\n" +
	"\x0eitem_sale_type\x18\x06 \x01(\x0e2\x18.MainServer.ItemSaleTypeR\fitemSaleType\x12\x1b\n" +
	"\tupdate_ts\x18\b \x01(\x03R\bupdateTs\x12@\n" +
	"\x0eequipment_type\x18\t \x01(\x0e2\x19.MainServer.EquipmentTypeR\requipmentType\"\x8a\x01\n" +
	"\x12SaleEquipmentParam\x12%\n" +
	"\x0eequipment_name\x18\x01 \x01(\tR\requipmentName\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12\x14\n" +
	"\x05price\x18\x03 \x01(\x05R\x05price\x12!\n" +
	"\fspecial_coin\x18\x04 \x01(\x05R\vspecialCoin\"S\n" +
	"\x14UnsaleEquipmentParam\x12%\n" +
	"\x0eequipment_name\x18\x01 \x01(\tR\requipmentName\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\"\xd1\x01\n" +
	"\x14QueryEquipmentsParam\x12%\n" +
	"\x0eequipment_name\x18\x01 \x01(\tR\requipmentName\x123\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1b.MainServer.EquipmentStatusR\x06status\x12\x1b\n" +
	"\tupdate_ts\x18\x03 \x01(\x03R\bupdateTs\x12@\n" +
	"\x0eequipment_type\x18\x04 \x01(\x0e2\x19.MainServer.EquipmentTypeR\requipmentType\"}\n" +
	"\x16EquipToDecorationParam\x12!\n" +
	"\fequipment_id\x18\x01 \x01(\x03R\vequipmentId\x12@\n" +
	"\x0eequipment_type\x18\x02 \x01(\x0e2\x19.MainServer.EquipmentTypeR\requipmentType\"\\\n" +
	"\x18UnequipToDecorationParam\x12@\n" +
	"\x0eequipment_type\x18\x01 \x01(\x0e2\x19.MainServer.EquipmentTypeR\requipmentType\"J\n" +
	"\x14AddEquipmentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"K\n" +
	"\x15SaleEquipmentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"M\n" +
	"\x17UnsaleEquipmentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"4\n" +
	"\x15GetAllEquipmentsParam\x12\x1b\n" +
	"\tupdate_ts\x18\x01 \x01(\x03R\bupdateTs\"k\n" +
	"\x18GetAllEquipmentsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x125\n" +
	"\n" +
	"equipments\x18\x02 \x03(\v2\x15.MainServer.EquipmentR\n" +
	"equipments\"j\n" +
	"\x17QueryEquipmentsResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x125\n" +
	"\n" +
	"equipments\x18\x02 \x03(\v2\x15.MainServer.EquipmentR\n" +
	"equipments*\xa2\x01\n" +
	"\rEquipmentType\x12\x11\n" +
	"\requipment_nor\x10\x00\x12\x13\n" +
	"\x0fequipment_title\x10\x01\x12\x12\n" +
	"\x0eequipment_card\x10\x02\x12\x12\n" +
	"\x0eequipment_ride\x10\x03\x12\x16\n" +
	"\x12equipment_pokeball\x10\x04\x12\x13\n" +
	"\x0fequipment_badge\x10\x05\x12\x14\n" +
	"\x10equipment_amulet\x10\x06*\x94\x01\n" +
	"\x13FortifyMaterialType\x12\x1f\n" +
	"\x1bFortifyMaterialType_Unknown\x10\x00\x12\x1c\n" +
	"\x18FortifyMaterialType_Item\x10\x01\x12 \n" +
	"\x1cFortifyMaterialType_Euipment\x10\x02\x12\x1c\n" +
	"\x18FortifyMaterialType_Poke\x10\x03*\x7f\n" +
	"\x0fEquipmentStatus\x12\x1b\n" +
	"\x17EquipmentStatus_Unknown\x10\x00\x12\x1a\n" +
	"\x16EquipmentStatus_Normal\x10\x01\x12\x18\n" +
	"\x14EquipmentStatus_Sale\x10\x02\x12\x19\n" +
	"\x15EquipmentStatus_Equip\x10\x03*\x81\x06\n" +
	"\x1aTrainerEquipmentEffectType\x12\x18\n" +
	"\x14EquipmentEffect_None\x10\x00\x12\"\n" +
	"\x1eEquipmentEffect_EncounterEnemy\x10\x01\x12\"\n" +
	"\x1eEquipmentEffect_EncounterShine\x10\x02\x12&\n" +
	"\"EquipmentEffect_EncounterGoodEnemy\x10\x03\x12!\n" +
	"\x1dEquipmentEffect_GetBattlePoke\x10\x04\x12*\n" +
	"&EquipmentEffect_GetWildBattleEquipment\x10\x05\x12(\n" +
	"$EquipmentEffect_FishingGoodEquipment\x10\x06\x12*\n" +
	"&EquipmentEffect_FishingBattleEquipment\x10\a\x12 \n" +
	"\x1cEquipmentEffect_GetBattleExp\x10e\x12\"\n" +
	"\x1eEquipmentEffect_GetBattleMoney\x10f\x12&\n" +
	"!EquipmentEffect_BreedGoodPokebaby\x10\xc9\x01\x12'\n" +
	"\"EquipmentEffect_BreedDifficultBaby\x10\xca\x01\x12$\n" +
	"\x1fEquipmentEffect_FishingGoodPoke\x10\xcb\x01\x12!\n" +
	"\x1cEquipmentEffect_GetBattleEvs\x10\xcc\x01\x12!\n" +
	"\x1cEquipmentEffect_HatchEggTime\x10\xcd\x01\x12\x1f\n" +
	"\x1aEquipmentEffect_BreedShine\x10\xce\x01\x12$\n" +
	"\x1fEquipmentEffect_TreeFruitGrowup\x10\xad\x02\x12&\n" +
	"!EquipmentEffect_TreeFruitWithered\x10\xae\x02\x12#\n" +
	"\x1eEquipmentEffect_BorrowGoodPoke\x10\x85\a\x12\x1d\n" +
	"\x18EquipmentEffect_TeamBind\x10\x86\a*\xe2\x03\n" +
	"\x1eTrainerEquipmentEffectPokeType\x12\x16\n" +
	"\x12EffectPoke_Unknown\x10\x00\x12\x15\n" +
	"\x11EffectPoke_Normal\x10\x01\x12\x13\n" +
	"\x0fEffectPoke_Fire\x10\x02\x12\x14\n" +
	"\x10EffectPoke_Water\x10\x03\x12\x17\n" +
	"\x13EffectPoke_Electric\x10\x04\x12\x14\n" +
	"\x10EffectPoke_Grass\x10\x05\x12\x12\n" +
	"\x0eEffectPoke_Ice\x10\x06\x12\x17\n" +
	"\x13EffectPoke_Fighting\x10\a\x12\x15\n" +
	"\x11EffectPoke_Poison\x10\b\x12\x15\n" +
	"\x11EffectPoke_Ground\x10\t\x12\x15\n" +
	"\x11EffectPoke_Flying\x10\n" +
	"\x12\x16\n" +
	"\x12EffectPoke_Psychic\x10\v\x12\x12\n" +
	"\x0eEffectPoke_Bug\x10\f\x12\x13\n" +
	"\x0fEffectPoke_Rock\x10\r\x12\x14\n" +
	"\x10EffectPoke_Ghost\x10\x0e\x12\x15\n" +
	"\x11EffectPoke_Dragon\x10\x0f\x12\x13\n" +
	"\x0fEffectPoke_Dark\x10\x10\x12\x14\n" +
	"\x10EffectPoke_Steel\x10\x11\x12\x14\n" +
	"\x10EffectPoke_Fairy\x10\x12\x12\x16\n" +
	"\x12EffectPoke_Stellar\x10\x13B!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Equipment_proto_rawDescOnce sync.Once
	file_MainServer_Equipment_proto_rawDescData []byte
)

func file_MainServer_Equipment_proto_rawDescGZIP() []byte {
	file_MainServer_Equipment_proto_rawDescOnce.Do(func() {
		file_MainServer_Equipment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Equipment_proto_rawDesc), len(file_MainServer_Equipment_proto_rawDesc)))
	})
	return file_MainServer_Equipment_proto_rawDescData
}

var file_MainServer_Equipment_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_MainServer_Equipment_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_MainServer_Equipment_proto_goTypes = []any{
	(EquipmentType)(0),                               // 0: MainServer.EquipmentType
	(FortifyMaterialType)(0),                         // 1: MainServer.FortifyMaterialType
	(EquipmentStatus)(0),                             // 2: MainServer.EquipmentStatus
	(TrainerEquipmentEffectType)(0),                  // 3: MainServer.TrainerEquipmentEffectType
	(TrainerEquipmentEffectPokeType)(0),              // 4: MainServer.TrainerEquipmentEffectPokeType
	(*EquipmentSlot)(nil),                            // 5: MainServer.EquipmentSlot
	(*EquipmentList)(nil),                            // 6: MainServer.EquipmentList
	(*FortifyMaterial)(nil),                          // 7: MainServer.FortifyMaterial
	(*EquipmentConfigInfo)(nil),                      // 8: MainServer.EquipmentConfigInfo
	(*EquipmentConfigValue)(nil),                     // 9: MainServer.EquipmentConfigValue
	(*Equipment)(nil),                                // 10: MainServer.Equipment
	(*TrainerEquipmentEffectPokeAllOneTypeInfo)(nil), // 11: MainServer.TrainerEquipmentEffectPokeAllOneTypeInfo
	(*TrainerEquipmentEffect)(nil),                   // 12: MainServer.TrainerEquipmentEffect
	(*TrainerEquipmentAllOneEffect)(nil),             // 13: MainServer.TrainerEquipmentAllOneEffect
	(*EquipmentFilter)(nil),                          // 14: MainServer.EquipmentFilter
	(*SaleEquipmentParam)(nil),                       // 15: MainServer.SaleEquipmentParam
	(*UnsaleEquipmentParam)(nil),                     // 16: MainServer.UnsaleEquipmentParam
	(*QueryEquipmentsParam)(nil),                     // 17: MainServer.QueryEquipmentsParam
	(*EquipToDecorationParam)(nil),                   // 18: MainServer.EquipToDecorationParam
	(*UnequipToDecorationParam)(nil),                 // 19: MainServer.UnequipToDecorationParam
	(*AddEquipmentResponse)(nil),                     // 20: MainServer.AddEquipmentResponse
	(*SaleEquipmentResponse)(nil),                    // 21: MainServer.SaleEquipmentResponse
	(*UnsaleEquipmentResponse)(nil),                  // 22: MainServer.UnsaleEquipmentResponse
	(*GetAllEquipmentsParam)(nil),                    // 23: MainServer.GetAllEquipmentsParam
	(*GetAllEquipmentsResponse)(nil),                 // 24: MainServer.GetAllEquipmentsResponse
	(*QueryEquipmentsResponse)(nil),                  // 25: MainServer.QueryEquipmentsResponse
	nil,                                              // 26: MainServer.EquipmentList.EquipmentMapEntry
	nil,                                              // 27: MainServer.TrainerEquipmentEffect.EquipmentEffectEntry
	nil,                                              // 28: MainServer.TrainerEquipmentEffect.EquipmentPokeEffectEntry
	(TrainerTeam)(0),                                 // 29: MainServer.TrainerTeam
	(ItemSaleType)(0),                                // 30: MainServer.ItemSaleType
}
var file_MainServer_Equipment_proto_depIdxs = []int32{
	26, // 0: MainServer.EquipmentList.equipment_map:type_name -> MainServer.EquipmentList.EquipmentMapEntry
	1,  // 1: MainServer.FortifyMaterial.material_type:type_name -> MainServer.FortifyMaterialType
	29, // 2: MainServer.EquipmentConfigInfo.team_type:type_name -> MainServer.TrainerTeam
	30, // 3: MainServer.EquipmentConfigInfo.item_sale_type:type_name -> MainServer.ItemSaleType
	0,  // 4: MainServer.EquipmentConfigInfo.equipment_type:type_name -> MainServer.EquipmentType
	9,  // 5: MainServer.EquipmentConfigInfo.all_equipments:type_name -> MainServer.EquipmentConfigValue
	9,  // 6: MainServer.EquipmentConfigInfo.one_equipments:type_name -> MainServer.EquipmentConfigValue
	9,  // 7: MainServer.EquipmentConfigInfo.two_equipments:type_name -> MainServer.EquipmentConfigValue
	9,  // 8: MainServer.EquipmentConfigInfo.three_equipments:type_name -> MainServer.EquipmentConfigValue
	9,  // 9: MainServer.EquipmentConfigInfo.sp_all_equipments:type_name -> MainServer.EquipmentConfigValue
	7,  // 10: MainServer.EquipmentConfigInfo.fortify_materials:type_name -> MainServer.FortifyMaterial
	3,  // 11: MainServer.EquipmentConfigValue.effect_type:type_name -> MainServer.TrainerEquipmentEffectType
	4,  // 12: MainServer.EquipmentConfigValue.effect_poke_type:type_name -> MainServer.TrainerEquipmentEffectPokeType
	29, // 13: MainServer.Equipment.team_type:type_name -> MainServer.TrainerTeam
	2,  // 14: MainServer.Equipment.status:type_name -> MainServer.EquipmentStatus
	0,  // 15: MainServer.Equipment.equipment_type:type_name -> MainServer.EquipmentType
	30, // 16: MainServer.Equipment.item_sale_type:type_name -> MainServer.ItemSaleType
	12, // 17: MainServer.Equipment.effect_info:type_name -> MainServer.TrainerEquipmentEffect
	27, // 18: MainServer.TrainerEquipmentEffect.equipmentEffect:type_name -> MainServer.TrainerEquipmentEffect.EquipmentEffectEntry
	28, // 19: MainServer.TrainerEquipmentEffect.equipmentPokeEffect:type_name -> MainServer.TrainerEquipmentEffect.EquipmentPokeEffectEntry
	11, // 20: MainServer.TrainerEquipmentAllOneEffect.effectPokeTypeInfo:type_name -> MainServer.TrainerEquipmentEffectPokeAllOneTypeInfo
	2,  // 21: MainServer.EquipmentFilter.status:type_name -> MainServer.EquipmentStatus
	30, // 22: MainServer.EquipmentFilter.item_sale_type:type_name -> MainServer.ItemSaleType
	0,  // 23: MainServer.EquipmentFilter.equipment_type:type_name -> MainServer.EquipmentType
	2,  // 24: MainServer.QueryEquipmentsParam.status:type_name -> MainServer.EquipmentStatus
	0,  // 25: MainServer.QueryEquipmentsParam.equipment_type:type_name -> MainServer.EquipmentType
	0,  // 26: MainServer.EquipToDecorationParam.equipment_type:type_name -> MainServer.EquipmentType
	0,  // 27: MainServer.UnequipToDecorationParam.equipment_type:type_name -> MainServer.EquipmentType
	10, // 28: MainServer.GetAllEquipmentsResponse.equipments:type_name -> MainServer.Equipment
	10, // 29: MainServer.QueryEquipmentsResponse.equipments:type_name -> MainServer.Equipment
	8,  // 30: MainServer.EquipmentList.EquipmentMapEntry.value:type_name -> MainServer.EquipmentConfigInfo
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_MainServer_Equipment_proto_init() }
func file_MainServer_Equipment_proto_init() {
	if File_MainServer_Equipment_proto != nil {
		return
	}
	file_MainServer_TrainerTeam_proto_init()
	file_MainServer_Inventory_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Equipment_proto_rawDesc), len(file_MainServer_Equipment_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Equipment_proto_goTypes,
		DependencyIndexes: file_MainServer_Equipment_proto_depIdxs,
		EnumInfos:         file_MainServer_Equipment_proto_enumTypes,
		MessageInfos:      file_MainServer_Equipment_proto_msgTypes,
	}.Build()
	File_MainServer_Equipment_proto = out.File
	file_MainServer_Equipment_proto_goTypes = nil
	file_MainServer_Equipment_proto_depIdxs = nil
}
