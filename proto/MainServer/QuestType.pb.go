// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/QuestType.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// import "MainServer/Poke.proto";
//
//	message QuestTypeList {
//	    repeated QuestType quest_type_list = 1;
//	}
type QuestType int32

const (
	QuestType_QuestType_once                    QuestType = 0  // 一次性任务
	QuestType_QuestType_daily                   QuestType = 1  // 每日任务
	QuestType_QuestType_weekly                  QuestType = 2  // 每周任务
	QuestType_QuestType_monthly                 QuestType = 3  // 每月任务
	QuestType_QuestType_yearly                  QuestType = 4  // 每年任务
	QuestType_QuestType_repeat                  QuestType = 5  // 重复任务
	QuestType_QuestType_custom                  QuestType = 6  // 自定义任务
	QuestType_QuestType_activity                QuestType = 7  // 活动任务
	QuestType_QuestType_activity_original_first QuestType = 8  // 活动任务-原初任务
	QuestType_QuestType_team                    QuestType = 9  // 团队任务
	QuestType_QuestType_Instance                QuestType = 10 // 副本任务
)

// Enum value maps for QuestType.
var (
	QuestType_name = map[int32]string{
		0:  "QuestType_once",
		1:  "QuestType_daily",
		2:  "QuestType_weekly",
		3:  "QuestType_monthly",
		4:  "QuestType_yearly",
		5:  "QuestType_repeat",
		6:  "QuestType_custom",
		7:  "QuestType_activity",
		8:  "QuestType_activity_original_first",
		9:  "QuestType_team",
		10: "QuestType_Instance",
	}
	QuestType_value = map[string]int32{
		"QuestType_once":                    0,
		"QuestType_daily":                   1,
		"QuestType_weekly":                  2,
		"QuestType_monthly":                 3,
		"QuestType_yearly":                  4,
		"QuestType_repeat":                  5,
		"QuestType_custom":                  6,
		"QuestType_activity":                7,
		"QuestType_activity_original_first": 8,
		"QuestType_team":                    9,
		"QuestType_Instance":                10,
	}
)

func (x QuestType) Enum() *QuestType {
	p := new(QuestType)
	*p = x
	return p
}

func (x QuestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[0].Descriptor()
}

func (QuestType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[0]
}

func (x QuestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestType.Descriptor instead.
func (QuestType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{0}
}

type QuestStatus int32

const (
	QuestStatus_QuestStatus_close QuestStatus = 0 // 关闭 关闭状态不可见
	QuestStatus_QuestStatus_wait  QuestStatus = 1 // 等待时间到开启
	QuestStatus_QuestStatus_open  QuestStatus = 2 // 开启 开启状态可见
)

// Enum value maps for QuestStatus.
var (
	QuestStatus_name = map[int32]string{
		0: "QuestStatus_close",
		1: "QuestStatus_wait",
		2: "QuestStatus_open",
	}
	QuestStatus_value = map[string]int32{
		"QuestStatus_close": 0,
		"QuestStatus_wait":  1,
		"QuestStatus_open":  2,
	}
)

func (x QuestStatus) Enum() *QuestStatus {
	p := new(QuestStatus)
	*p = x
	return p
}

func (x QuestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[1].Descriptor()
}

func (QuestStatus) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[1]
}

func (x QuestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestStatus.Descriptor instead.
func (QuestStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{1}
}

type QuestUnlockType int32

const (
	QuestUnlockType_QuestUnlockType_none              QuestUnlockType = 0  // 等级解锁
	QuestUnlockType_QuestUnlockType_quest             QuestUnlockType = 1  // 任务解锁
	QuestUnlockType_QuestUnlockType_item              QuestUnlockType = 2  // 道具解锁
	QuestUnlockType_QuestUnlockType_money             QuestUnlockType = 3  // 金钱解锁
	QuestUnlockType_QuestUnlockType_time              QuestUnlockType = 5  // 时间解锁
	QuestUnlockType_QuestUnlockType_poke              QuestUnlockType = 6  // poke解锁
	QuestUnlockType_QuestUnlockType_achievement       QuestUnlockType = 7  // 成就解锁
	QuestUnlockType_QuestUnlockType_title             QuestUnlockType = 8  // 称号解锁
	QuestUnlockType_QuestUnlockType_battle_level      QuestUnlockType = 9  // 战斗等级解锁
	QuestUnlockType_QuestUnlockType_team              QuestUnlockType = 10 // 团队解锁
	QuestUnlockType_QuestUnlockType_team_strict       QuestUnlockType = 11 // 限制团队
	QuestUnlockType_QuestUnlockType_team_contribution QuestUnlockType = 12 // 团队贡献
	QuestUnlockType_QuestUnlockType_team_level        QuestUnlockType = 13 // 团队等级
	QuestUnlockType_QuestUnlockType_item_useing       QuestUnlockType = 14 // 道具正常使用中
)

// Enum value maps for QuestUnlockType.
var (
	QuestUnlockType_name = map[int32]string{
		0:  "QuestUnlockType_none",
		1:  "QuestUnlockType_quest",
		2:  "QuestUnlockType_item",
		3:  "QuestUnlockType_money",
		5:  "QuestUnlockType_time",
		6:  "QuestUnlockType_poke",
		7:  "QuestUnlockType_achievement",
		8:  "QuestUnlockType_title",
		9:  "QuestUnlockType_battle_level",
		10: "QuestUnlockType_team",
		11: "QuestUnlockType_team_strict",
		12: "QuestUnlockType_team_contribution",
		13: "QuestUnlockType_team_level",
		14: "QuestUnlockType_item_useing",
	}
	QuestUnlockType_value = map[string]int32{
		"QuestUnlockType_none":              0,
		"QuestUnlockType_quest":             1,
		"QuestUnlockType_item":              2,
		"QuestUnlockType_money":             3,
		"QuestUnlockType_time":              5,
		"QuestUnlockType_poke":              6,
		"QuestUnlockType_achievement":       7,
		"QuestUnlockType_title":             8,
		"QuestUnlockType_battle_level":      9,
		"QuestUnlockType_team":              10,
		"QuestUnlockType_team_strict":       11,
		"QuestUnlockType_team_contribution": 12,
		"QuestUnlockType_team_level":        13,
		"QuestUnlockType_item_useing":       14,
	}
)

func (x QuestUnlockType) Enum() *QuestUnlockType {
	p := new(QuestUnlockType)
	*p = x
	return p
}

func (x QuestUnlockType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestUnlockType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[2].Descriptor()
}

func (QuestUnlockType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[2]
}

func (x QuestUnlockType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestUnlockType.Descriptor instead.
func (QuestUnlockType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{2}
}

type QuestCompleteType int32

const (
	QuestCompleteType_QuestCompleteType_none         QuestCompleteType = 0  // 无
	QuestCompleteType_QuestCompleteType_battle_poke  QuestCompleteType = 1  // 击败poke
	QuestCompleteType_QuestCompleteType_battle_npc   QuestCompleteType = 2  // 击败某个npc
	QuestCompleteType_QuestCompleteType_collect_item QuestCompleteType = 3  // 收集道具
	QuestCompleteType_QuestCompleteType_collect_poke QuestCompleteType = 4  // 收集poke
	QuestCompleteType_QuestCompleteType_talk         QuestCompleteType = 5  // 和某个npc交谈
	QuestCompleteType_QuestCompleteType_deliver_item QuestCompleteType = 6  // 交付道具
	QuestCompleteType_QuestCompleteType_deliver_poke QuestCompleteType = 7  // 交付poke
	QuestCompleteType_QuestCompleteType_arrive_area  QuestCompleteType = 8  // 到达某个地点 （可以用name，比如添加一个碰撞体，到达那之后，就传送这个碰撞体的名称）
	QuestCompleteType_QuestCompleteType_custom       QuestCompleteType = 9  // 自定义
	QuestCompleteType_QuestCompleteType_limit_time   QuestCompleteType = 10 // 完成时间
)

// Enum value maps for QuestCompleteType.
var (
	QuestCompleteType_name = map[int32]string{
		0:  "QuestCompleteType_none",
		1:  "QuestCompleteType_battle_poke",
		2:  "QuestCompleteType_battle_npc",
		3:  "QuestCompleteType_collect_item",
		4:  "QuestCompleteType_collect_poke",
		5:  "QuestCompleteType_talk",
		6:  "QuestCompleteType_deliver_item",
		7:  "QuestCompleteType_deliver_poke",
		8:  "QuestCompleteType_arrive_area",
		9:  "QuestCompleteType_custom",
		10: "QuestCompleteType_limit_time",
	}
	QuestCompleteType_value = map[string]int32{
		"QuestCompleteType_none":         0,
		"QuestCompleteType_battle_poke":  1,
		"QuestCompleteType_battle_npc":   2,
		"QuestCompleteType_collect_item": 3,
		"QuestCompleteType_collect_poke": 4,
		"QuestCompleteType_talk":         5,
		"QuestCompleteType_deliver_item": 6,
		"QuestCompleteType_deliver_poke": 7,
		"QuestCompleteType_arrive_area":  8,
		"QuestCompleteType_custom":       9,
		"QuestCompleteType_limit_time":   10,
	}
)

func (x QuestCompleteType) Enum() *QuestCompleteType {
	p := new(QuestCompleteType)
	*p = x
	return p
}

func (x QuestCompleteType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestCompleteType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[3].Descriptor()
}

func (QuestCompleteType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[3]
}

func (x QuestCompleteType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestCompleteType.Descriptor instead.
func (QuestCompleteType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{3}
}

type QuestBroadcastType int32

const (
	QuestBroadcastType_QuestBroadcast_none   QuestBroadcastType = 0 // 无
	QuestBroadcastType_QuestBroadcast_system QuestBroadcastType = 1 // 系统广播
	QuestBroadcastType_QuestBroadcast_world  QuestBroadcastType = 2 // 世界广播
	QuestBroadcastType_QuestBroadcast_area   QuestBroadcastType = 3 // 地区广播
)

// Enum value maps for QuestBroadcastType.
var (
	QuestBroadcastType_name = map[int32]string{
		0: "QuestBroadcast_none",
		1: "QuestBroadcast_system",
		2: "QuestBroadcast_world",
		3: "QuestBroadcast_area",
	}
	QuestBroadcastType_value = map[string]int32{
		"QuestBroadcast_none":   0,
		"QuestBroadcast_system": 1,
		"QuestBroadcast_world":  2,
		"QuestBroadcast_area":   3,
	}
)

func (x QuestBroadcastType) Enum() *QuestBroadcastType {
	p := new(QuestBroadcastType)
	*p = x
	return p
}

func (x QuestBroadcastType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestBroadcastType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[4].Descriptor()
}

func (QuestBroadcastType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[4]
}

func (x QuestBroadcastType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestBroadcastType.Descriptor instead.
func (QuestBroadcastType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{4}
}

type RewardType int32

const (
	RewardType_RewardType_None           RewardType = 0
	RewardType_RewardType_Pokemon        RewardType = 1  //宝可梦
	RewardType_RewardType_Item           RewardType = 2  //道具
	RewardType_RewardType_Cloth          RewardType = 3  //服装
	RewardType_RewardType_Money          RewardType = 4  //金钱
	RewardType_RewardType_Exp            RewardType = 5  //经验值
	RewardType_RewardType_Star           RewardType = 6  //星星
	RewardType_RewardType_Title          RewardType = 7  //称号
	RewardType_RewardType_Achievement    RewardType = 8  //成就
	RewardType_RewardType_BattleLevel    RewardType = 9  //战斗等级
	RewardType_RewardType_Config_Pokemon RewardType = 10 //配置的宝可梦
	RewardType_RewardType_Sp_Badge       RewardType = 11 //徽章
	RewardType_RewardType_Sp_Champion    RewardType = 12 //冠军
	RewardType_RewardType_Equip          RewardType = 13 //装备
)

// Enum value maps for RewardType.
var (
	RewardType_name = map[int32]string{
		0:  "RewardType_None",
		1:  "RewardType_Pokemon",
		2:  "RewardType_Item",
		3:  "RewardType_Cloth",
		4:  "RewardType_Money",
		5:  "RewardType_Exp",
		6:  "RewardType_Star",
		7:  "RewardType_Title",
		8:  "RewardType_Achievement",
		9:  "RewardType_BattleLevel",
		10: "RewardType_Config_Pokemon",
		11: "RewardType_Sp_Badge",
		12: "RewardType_Sp_Champion",
		13: "RewardType_Equip",
	}
	RewardType_value = map[string]int32{
		"RewardType_None":           0,
		"RewardType_Pokemon":        1,
		"RewardType_Item":           2,
		"RewardType_Cloth":          3,
		"RewardType_Money":          4,
		"RewardType_Exp":            5,
		"RewardType_Star":           6,
		"RewardType_Title":          7,
		"RewardType_Achievement":    8,
		"RewardType_BattleLevel":    9,
		"RewardType_Config_Pokemon": 10,
		"RewardType_Sp_Badge":       11,
		"RewardType_Sp_Champion":    12,
		"RewardType_Equip":          13,
	}
)

func (x RewardType) Enum() *RewardType {
	p := new(RewardType)
	*p = x
	return p
}

func (x RewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_QuestType_proto_enumTypes[5].Descriptor()
}

func (RewardType) Type() protoreflect.EnumType {
	return &file_MainServer_QuestType_proto_enumTypes[5]
}

func (x RewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardType.Descriptor instead.
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_QuestType_proto_rawDescGZIP(), []int{5}
}

var File_MainServer_QuestType_proto protoreflect.FileDescriptor

const file_MainServer_QuestType_proto_rawDesc = "" +
	"\n" +
	"\x1aMainServer/QuestType.proto\x12\n" +
	"MainServer*\x8e\x02\n" +
	"\tQuestType\x12\x12\n" +
	"\x0eQuestType_once\x10\x00\x12\x13\n" +
	"\x0fQuestType_daily\x10\x01\x12\x14\n" +
	"\x10QuestType_weekly\x10\x02\x12\x15\n" +
	"\x11QuestType_monthly\x10\x03\x12\x14\n" +
	"\x10QuestType_yearly\x10\x04\x12\x14\n" +
	"\x10QuestType_repeat\x10\x05\x12\x14\n" +
	"\x10QuestType_custom\x10\x06\x12\x16\n" +
	"\x12QuestType_activity\x10\a\x12%\n" +
	"!QuestType_activity_original_first\x10\b\x12\x12\n" +
	"\x0eQuestType_team\x10\t\x12\x16\n" +
	"\x12QuestType_Instance\x10\n" +
	"*P\n" +
	"\vQuestStatus\x12\x15\n" +
	"\x11QuestStatus_close\x10\x00\x12\x14\n" +
	"\x10QuestStatus_wait\x10\x01\x12\x14\n" +
	"\x10QuestStatus_open\x10\x02*\xb0\x03\n" +
	"\x0fQuestUnlockType\x12\x18\n" +
	"\x14QuestUnlockType_none\x10\x00\x12\x19\n" +
	"\x15QuestUnlockType_quest\x10\x01\x12\x18\n" +
	"\x14QuestUnlockType_item\x10\x02\x12\x19\n" +
	"\x15QuestUnlockType_money\x10\x03\x12\x18\n" +
	"\x14QuestUnlockType_time\x10\x05\x12\x18\n" +
	"\x14QuestUnlockType_poke\x10\x06\x12\x1f\n" +
	"\x1bQuestUnlockType_achievement\x10\a\x12\x19\n" +
	"\x15QuestUnlockType_title\x10\b\x12 \n" +
	"\x1cQuestUnlockType_battle_level\x10\t\x12\x18\n" +
	"\x14QuestUnlockType_team\x10\n" +
	"\x12\x1f\n" +
	"\x1bQuestUnlockType_team_strict\x10\v\x12%\n" +
	"!QuestUnlockType_team_contribution\x10\f\x12\x1e\n" +
	"\x1aQuestUnlockType_team_level\x10\r\x12\x1f\n" +
	"\x1bQuestUnlockType_item_useing\x10\x0e*\x83\x03\n" +
	"\x11QuestCompleteType\x12\x1a\n" +
	"\x16QuestCompleteType_none\x10\x00\x12!\n" +
	"\x1dQuestCompleteType_battle_poke\x10\x01\x12 \n" +
	"\x1cQuestCompleteType_battle_npc\x10\x02\x12\"\n" +
	"\x1eQuestCompleteType_collect_item\x10\x03\x12\"\n" +
	"\x1eQuestCompleteType_collect_poke\x10\x04\x12\x1a\n" +
	"\x16QuestCompleteType_talk\x10\x05\x12\"\n" +
	"\x1eQuestCompleteType_deliver_item\x10\x06\x12\"\n" +
	"\x1eQuestCompleteType_deliver_poke\x10\a\x12!\n" +
	"\x1dQuestCompleteType_arrive_area\x10\b\x12\x1c\n" +
	"\x18QuestCompleteType_custom\x10\t\x12 \n" +
	"\x1cQuestCompleteType_limit_time\x10\n" +
	"*{\n" +
	"\x12QuestBroadcastType\x12\x17\n" +
	"\x13QuestBroadcast_none\x10\x00\x12\x19\n" +
	"\x15QuestBroadcast_system\x10\x01\x12\x18\n" +
	"\x14QuestBroadcast_world\x10\x02\x12\x17\n" +
	"\x13QuestBroadcast_area\x10\x03*\xdb\x02\n" +
	"\n" +
	"RewardType\x12\x13\n" +
	"\x0fRewardType_None\x10\x00\x12\x16\n" +
	"\x12RewardType_Pokemon\x10\x01\x12\x13\n" +
	"\x0fRewardType_Item\x10\x02\x12\x14\n" +
	"\x10RewardType_Cloth\x10\x03\x12\x14\n" +
	"\x10RewardType_Money\x10\x04\x12\x12\n" +
	"\x0eRewardType_Exp\x10\x05\x12\x13\n" +
	"\x0fRewardType_Star\x10\x06\x12\x14\n" +
	"\x10RewardType_Title\x10\a\x12\x1a\n" +
	"\x16RewardType_Achievement\x10\b\x12\x1a\n" +
	"\x16RewardType_BattleLevel\x10\t\x12\x1d\n" +
	"\x19RewardType_Config_Pokemon\x10\n" +
	"\x12\x17\n" +
	"\x13RewardType_Sp_Badge\x10\v\x12\x1a\n" +
	"\x16RewardType_Sp_Champion\x10\f\x12\x14\n" +
	"\x10RewardType_Equip\x10\rB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_QuestType_proto_rawDescOnce sync.Once
	file_MainServer_QuestType_proto_rawDescData []byte
)

func file_MainServer_QuestType_proto_rawDescGZIP() []byte {
	file_MainServer_QuestType_proto_rawDescOnce.Do(func() {
		file_MainServer_QuestType_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_QuestType_proto_rawDesc), len(file_MainServer_QuestType_proto_rawDesc)))
	})
	return file_MainServer_QuestType_proto_rawDescData
}

var file_MainServer_QuestType_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_MainServer_QuestType_proto_goTypes = []any{
	(QuestType)(0),          // 0: MainServer.QuestType
	(QuestStatus)(0),        // 1: MainServer.QuestStatus
	(QuestUnlockType)(0),    // 2: MainServer.QuestUnlockType
	(QuestCompleteType)(0),  // 3: MainServer.QuestCompleteType
	(QuestBroadcastType)(0), // 4: MainServer.QuestBroadcastType
	(RewardType)(0),         // 5: MainServer.RewardType
}
var file_MainServer_QuestType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_MainServer_QuestType_proto_init() }
func file_MainServer_QuestType_proto_init() {
	if File_MainServer_QuestType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_QuestType_proto_rawDesc), len(file_MainServer_QuestType_proto_rawDesc)),
			NumEnums:      6,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_QuestType_proto_goTypes,
		DependencyIndexes: file_MainServer_QuestType_proto_depIdxs,
		EnumInfos:         file_MainServer_QuestType_proto_enumTypes,
	}.Build()
	File_MainServer_QuestType_proto = out.File
	file_MainServer_QuestType_proto_goTypes = nil
	file_MainServer_QuestType_proto_depIdxs = nil
}
