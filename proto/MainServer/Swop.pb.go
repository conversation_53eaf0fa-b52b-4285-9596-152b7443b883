// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Swop.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SwopState int32

const (
	SwopState_INIT       SwopState = 0
	SwopState_REQUESTED  SwopState = 1
	SwopState_ACCEPTED   SwopState = 2
	SwopState_SELECTING  SwopState = 3
	SwopState_SUBMITTED  SwopState = 4
	SwopState_CONFIRMING SwopState = 5
	SwopState_CONFIRMED  SwopState = 6
	SwopState_COMPLETED  SwopState = 7
	SwopState_CANCELLED  SwopState = 8
	SwopState_CLOSED     SwopState = 9
)

// Enum value maps for SwopState.
var (
	SwopState_name = map[int32]string{
		0: "INIT",
		1: "REQUESTED",
		2: "ACCEPTED",
		3: "SELECTING",
		4: "SUBMITTED",
		5: "CONFIRMING",
		6: "CONFIRMED",
		7: "COMPLETED",
		8: "CANCELLED",
		9: "CLOSED",
	}
	SwopState_value = map[string]int32{
		"INIT":       0,
		"REQUESTED":  1,
		"ACCEPTED":   2,
		"SELECTING":  3,
		"SUBMITTED":  4,
		"CONFIRMING": 5,
		"CONFIRMED":  6,
		"COMPLETED":  7,
		"CANCELLED":  8,
		"CLOSED":     9,
	}
)

func (x SwopState) Enum() *SwopState {
	p := new(SwopState)
	*p = x
	return p
}

func (x SwopState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SwopState) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Swop_proto_enumTypes[0].Descriptor()
}

func (SwopState) Type() protoreflect.EnumType {
	return &file_MainServer_Swop_proto_enumTypes[0]
}

func (x SwopState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SwopState.Descriptor instead.
func (SwopState) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{0}
}

type BuyInventoryItemInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Category      InventoryType          `protobuf:"varint,1,opt,name=category,proto3,enum=MainServer.InventoryType" json:"category,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Count         int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BuyInventoryItemInfo) Reset() {
	*x = BuyInventoryItemInfo{}
	mi := &file_MainServer_Swop_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BuyInventoryItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BuyInventoryItemInfo) ProtoMessage() {}

func (x *BuyInventoryItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BuyInventoryItemInfo.ProtoReflect.Descriptor instead.
func (*BuyInventoryItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{0}
}

func (x *BuyInventoryItemInfo) GetCategory() InventoryType {
	if x != nil {
		return x.Category
	}
	return InventoryType_inventory_nor
}

func (x *BuyInventoryItemInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BuyInventoryItemInfo) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SwopItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Count         int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Type          InventoryType          `protobuf:"varint,4,opt,name=type,proto3,enum=MainServer.InventoryType" json:"type,omitempty"`
	ItemSaleType  ItemSaleType           `protobuf:"varint,5,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.ItemSaleType" json:"item_sale_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwopItem) Reset() {
	*x = SwopItem{}
	mi := &file_MainServer_Swop_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwopItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopItem) ProtoMessage() {}

func (x *SwopItem) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopItem.ProtoReflect.Descriptor instead.
func (*SwopItem) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{1}
}

func (x *SwopItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwopItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwopItem) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *SwopItem) GetType() InventoryType {
	if x != nil {
		return x.Type
	}
	return InventoryType_inventory_nor
}

func (x *SwopItem) GetItemSaleType() ItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return ItemSaleType_ItemSaleType_Normal
}

type SwopContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeIds       []int64                `protobuf:"varint,1,rep,packed,name=poke_ids,json=pokeIds,proto3" json:"poke_ids,omitempty"`
	Pokes         []*Poke                `protobuf:"bytes,2,rep,name=pokes,proto3" json:"pokes,omitempty"`
	Items         []*SwopItem            `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	Coin          int64                  `protobuf:"varint,4,opt,name=coin,proto3" json:"coin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwopContent) Reset() {
	*x = SwopContent{}
	mi := &file_MainServer_Swop_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwopContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopContent) ProtoMessage() {}

func (x *SwopContent) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopContent.ProtoReflect.Descriptor instead.
func (*SwopContent) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{2}
}

func (x *SwopContent) GetPokeIds() []int64 {
	if x != nil {
		return x.PokeIds
	}
	return nil
}

func (x *SwopContent) GetPokes() []*Poke {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *SwopContent) GetItems() []*SwopItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *SwopContent) GetCoin() int64 {
	if x != nil {
		return x.Coin
	}
	return 0
}

type SwopInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SwopId           int64                  `protobuf:"varint,1,opt,name=swop_id,json=swopId,proto3" json:"swop_id,omitempty"`
	InitiatorId      int64                  `protobuf:"varint,2,opt,name=initiator_id,json=initiatorId,proto3" json:"initiator_id,omitempty"`
	TargetId         int64                  `protobuf:"varint,3,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	InitiatorState   SwopState              `protobuf:"varint,4,opt,name=initiator_state,json=initiatorState,proto3,enum=MainServer.SwopState" json:"initiator_state,omitempty"`
	TargetState      SwopState              `protobuf:"varint,5,opt,name=target_state,json=targetState,proto3,enum=MainServer.SwopState" json:"target_state,omitempty"`
	InitiatorContent *SwopContent           `protobuf:"bytes,6,opt,name=initiator_content,json=initiatorContent,proto3" json:"initiator_content,omitempty"`
	TargetContent    *SwopContent           `protobuf:"bytes,7,opt,name=target_content,json=targetContent,proto3" json:"target_content,omitempty"`
	CreateTs         int64                  `protobuf:"varint,8,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`
	UpdateTs         int64                  `protobuf:"varint,9,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SwopInfo) Reset() {
	*x = SwopInfo{}
	mi := &file_MainServer_Swop_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwopInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopInfo) ProtoMessage() {}

func (x *SwopInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopInfo.ProtoReflect.Descriptor instead.
func (*SwopInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{3}
}

func (x *SwopInfo) GetSwopId() int64 {
	if x != nil {
		return x.SwopId
	}
	return 0
}

func (x *SwopInfo) GetInitiatorId() int64 {
	if x != nil {
		return x.InitiatorId
	}
	return 0
}

func (x *SwopInfo) GetTargetId() int64 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

func (x *SwopInfo) GetInitiatorState() SwopState {
	if x != nil {
		return x.InitiatorState
	}
	return SwopState_INIT
}

func (x *SwopInfo) GetTargetState() SwopState {
	if x != nil {
		return x.TargetState
	}
	return SwopState_INIT
}

func (x *SwopInfo) GetInitiatorContent() *SwopContent {
	if x != nil {
		return x.InitiatorContent
	}
	return nil
}

func (x *SwopInfo) GetTargetContent() *SwopContent {
	if x != nil {
		return x.TargetContent
	}
	return nil
}

func (x *SwopInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *SwopInfo) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type SwopChangeContentInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SwopId        int64                  `protobuf:"varint,1,opt,name=swop_id,json=swopId,proto3" json:"swop_id,omitempty"`
	Content       *SwopContent           `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwopChangeContentInfo) Reset() {
	*x = SwopChangeContentInfo{}
	mi := &file_MainServer_Swop_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwopChangeContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwopChangeContentInfo) ProtoMessage() {}

func (x *SwopChangeContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Swop_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwopChangeContentInfo.ProtoReflect.Descriptor instead.
func (*SwopChangeContentInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Swop_proto_rawDescGZIP(), []int{4}
}

func (x *SwopChangeContentInfo) GetSwopId() int64 {
	if x != nil {
		return x.SwopId
	}
	return 0
}

func (x *SwopChangeContentInfo) GetContent() *SwopContent {
	if x != nil {
		return x.Content
	}
	return nil
}

var File_MainServer_Swop_proto protoreflect.FileDescriptor

const file_MainServer_Swop_proto_rawDesc = "" +
	"\n" +
	"\x15MainServer/Swop.proto\x12\n" +
	"MainServer\x1a\x1aMainServer/Inventory.proto\x1a\x15MainServer/Poke.proto\"w\n" +
	"\x14BuyInventoryItemInfo\x125\n" +
	"\bcategory\x18\x01 \x01(\x0e2\x19.MainServer.InventoryTypeR\bcategory\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\"\xb3\x01\n" +
	"\bSwopItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\x12-\n" +
	"\x04type\x18\x04 \x01(\x0e2\x19.MainServer.InventoryTypeR\x04type\x12>\n" +
	"\x0eitem_sale_type\x18\x05 \x01(\x0e2\x18.MainServer.ItemSaleTypeR\fitemSaleType\"\x90\x01\n" +
	"\vSwopContent\x12\x19\n" +
	"\bpoke_ids\x18\x01 \x03(\x03R\apokeIds\x12&\n" +
	"\x05pokes\x18\x02 \x03(\v2\x10.MainServer.PokeR\x05pokes\x12*\n" +
	"\x05items\x18\x03 \x03(\v2\x14.MainServer.SwopItemR\x05items\x12\x12\n" +
	"\x04coin\x18\x04 \x01(\x03R\x04coin\"\x9d\x03\n" +
	"\bSwopInfo\x12\x17\n" +
	"\aswop_id\x18\x01 \x01(\x03R\x06swopId\x12!\n" +
	"\finitiator_id\x18\x02 \x01(\x03R\vinitiatorId\x12\x1b\n" +
	"\ttarget_id\x18\x03 \x01(\x03R\btargetId\x12>\n" +
	"\x0finitiator_state\x18\x04 \x01(\x0e2\x15.MainServer.SwopStateR\x0einitiatorState\x128\n" +
	"\ftarget_state\x18\x05 \x01(\x0e2\x15.MainServer.SwopStateR\vtargetState\x12D\n" +
	"\x11initiator_content\x18\x06 \x01(\v2\x17.MainServer.SwopContentR\x10initiatorContent\x12>\n" +
	"\x0etarget_content\x18\a \x01(\v2\x17.MainServer.SwopContentR\rtargetContent\x12\x1b\n" +
	"\tcreate_ts\x18\b \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\t \x01(\x03R\bupdateTs\"c\n" +
	"\x15SwopChangeContentInfo\x12\x17\n" +
	"\aswop_id\x18\x01 \x01(\x03R\x06swopId\x121\n" +
	"\acontent\x18\x02 \x01(\v2\x17.MainServer.SwopContentR\acontent*\x99\x01\n" +
	"\tSwopState\x12\b\n" +
	"\x04INIT\x10\x00\x12\r\n" +
	"\tREQUESTED\x10\x01\x12\f\n" +
	"\bACCEPTED\x10\x02\x12\r\n" +
	"\tSELECTING\x10\x03\x12\r\n" +
	"\tSUBMITTED\x10\x04\x12\x0e\n" +
	"\n" +
	"CONFIRMING\x10\x05\x12\r\n" +
	"\tCONFIRMED\x10\x06\x12\r\n" +
	"\tCOMPLETED\x10\a\x12\r\n" +
	"\tCANCELLED\x10\b\x12\n" +
	"\n" +
	"\x06CLOSED\x10\tB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Swop_proto_rawDescOnce sync.Once
	file_MainServer_Swop_proto_rawDescData []byte
)

func file_MainServer_Swop_proto_rawDescGZIP() []byte {
	file_MainServer_Swop_proto_rawDescOnce.Do(func() {
		file_MainServer_Swop_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Swop_proto_rawDesc), len(file_MainServer_Swop_proto_rawDesc)))
	})
	return file_MainServer_Swop_proto_rawDescData
}

var file_MainServer_Swop_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_MainServer_Swop_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_MainServer_Swop_proto_goTypes = []any{
	(SwopState)(0),                // 0: MainServer.SwopState
	(*BuyInventoryItemInfo)(nil),  // 1: MainServer.BuyInventoryItemInfo
	(*SwopItem)(nil),              // 2: MainServer.SwopItem
	(*SwopContent)(nil),           // 3: MainServer.SwopContent
	(*SwopInfo)(nil),              // 4: MainServer.SwopInfo
	(*SwopChangeContentInfo)(nil), // 5: MainServer.SwopChangeContentInfo
	(InventoryType)(0),            // 6: MainServer.InventoryType
	(ItemSaleType)(0),             // 7: MainServer.ItemSaleType
	(*Poke)(nil),                  // 8: MainServer.Poke
}
var file_MainServer_Swop_proto_depIdxs = []int32{
	6,  // 0: MainServer.BuyInventoryItemInfo.category:type_name -> MainServer.InventoryType
	6,  // 1: MainServer.SwopItem.type:type_name -> MainServer.InventoryType
	7,  // 2: MainServer.SwopItem.item_sale_type:type_name -> MainServer.ItemSaleType
	8,  // 3: MainServer.SwopContent.pokes:type_name -> MainServer.Poke
	2,  // 4: MainServer.SwopContent.items:type_name -> MainServer.SwopItem
	0,  // 5: MainServer.SwopInfo.initiator_state:type_name -> MainServer.SwopState
	0,  // 6: MainServer.SwopInfo.target_state:type_name -> MainServer.SwopState
	3,  // 7: MainServer.SwopInfo.initiator_content:type_name -> MainServer.SwopContent
	3,  // 8: MainServer.SwopInfo.target_content:type_name -> MainServer.SwopContent
	3,  // 9: MainServer.SwopChangeContentInfo.content:type_name -> MainServer.SwopContent
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_MainServer_Swop_proto_init() }
func file_MainServer_Swop_proto_init() {
	if File_MainServer_Swop_proto != nil {
		return
	}
	file_MainServer_Inventory_proto_init()
	file_MainServer_Poke_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Swop_proto_rawDesc), len(file_MainServer_Swop_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Swop_proto_goTypes,
		DependencyIndexes: file_MainServer_Swop_proto_depIdxs,
		EnumInfos:         file_MainServer_Swop_proto_enumTypes,
		MessageInfos:      file_MainServer_Swop_proto_msgTypes,
	}.Build()
	File_MainServer_Swop_proto = out.File
	file_MainServer_Swop_proto_goTypes = nil
	file_MainServer_Swop_proto_depIdxs = nil
}
