// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: MainServer/Poke.proto

package MainServer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PokeLiveStatus int32

const (
	// PokeLiveStatus_Unknown = 0;
	PokeLiveStatus_PokeLiveStatus_Normal    PokeLiveStatus = 0
	PokeLiveStatus_PokeLiveStatus_Fainted   PokeLiveStatus = 1
	PokeLiveStatus_PokeLiveStatus_Frozen    PokeLiveStatus = 2
	PokeLiveStatus_PokeLiveStatus_Poisoned  PokeLiveStatus = 3
	PokeLiveStatus_PokeLiveStatus_Burned    PokeLiveStatus = 4
	PokeLiveStatus_PokeLiveStatus_Paralyzed PokeLiveStatus = 5
	PokeLiveStatus_PokeLiveStatus_Sleeping  PokeLiveStatus = 6
	PokeLiveStatus_PokeLiveStatus_Confused  PokeLiveStatus = 7
	PokeLiveStatus_PokeLiveStatus_Dead      PokeLiveStatus = 8 //死亡 //比如生育后
)

// Enum value maps for PokeLiveStatus.
var (
	PokeLiveStatus_name = map[int32]string{
		0: "PokeLiveStatus_Normal",
		1: "PokeLiveStatus_Fainted",
		2: "PokeLiveStatus_Frozen",
		3: "PokeLiveStatus_Poisoned",
		4: "PokeLiveStatus_Burned",
		5: "PokeLiveStatus_Paralyzed",
		6: "PokeLiveStatus_Sleeping",
		7: "PokeLiveStatus_Confused",
		8: "PokeLiveStatus_Dead",
	}
	PokeLiveStatus_value = map[string]int32{
		"PokeLiveStatus_Normal":    0,
		"PokeLiveStatus_Fainted":   1,
		"PokeLiveStatus_Frozen":    2,
		"PokeLiveStatus_Poisoned":  3,
		"PokeLiveStatus_Burned":    4,
		"PokeLiveStatus_Paralyzed": 5,
		"PokeLiveStatus_Sleeping":  6,
		"PokeLiveStatus_Confused":  7,
		"PokeLiveStatus_Dead":      8,
	}
)

func (x PokeLiveStatus) Enum() *PokeLiveStatus {
	p := new(PokeLiveStatus)
	*p = x
	return p
}

func (x PokeLiveStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeLiveStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[0].Descriptor()
}

func (PokeLiveStatus) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[0]
}

func (x PokeLiveStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeLiveStatus.Descriptor instead.
func (PokeLiveStatus) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{0}
}

type BornInfoType int32

const (
	BornInfoType_BornInfoType_Wild   BornInfoType = 0
	BornInfoType_BornInfoType_Egg    BornInfoType = 1 //孵蛋
	BornInfoType_BornInfoType_Summon BornInfoType = 2 //召唤
	BornInfoType_BornInfoType_Gift   BornInfoType = 3 //礼物
)

// Enum value maps for BornInfoType.
var (
	BornInfoType_name = map[int32]string{
		0: "BornInfoType_Wild",
		1: "BornInfoType_Egg",
		2: "BornInfoType_Summon",
		3: "BornInfoType_Gift",
	}
	BornInfoType_value = map[string]int32{
		"BornInfoType_Wild":   0,
		"BornInfoType_Egg":    1,
		"BornInfoType_Summon": 2,
		"BornInfoType_Gift":   3,
	}
)

func (x BornInfoType) Enum() *BornInfoType {
	p := new(BornInfoType)
	*p = x
	return p
}

func (x BornInfoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BornInfoType) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[1].Descriptor()
}

func (BornInfoType) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[1]
}

func (x BornInfoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BornInfoType.Descriptor instead.
func (BornInfoType) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{1}
}

type PokeTypeEnum int32

const (
	PokeTypeEnum_PokeType_Unknown  PokeTypeEnum = 0
	PokeTypeEnum_PokeType_Normal   PokeTypeEnum = 1
	PokeTypeEnum_PokeType_Fire     PokeTypeEnum = 2
	PokeTypeEnum_PokeType_Water    PokeTypeEnum = 3
	PokeTypeEnum_PokeType_Electric PokeTypeEnum = 4
	PokeTypeEnum_PokeType_Grass    PokeTypeEnum = 5
	PokeTypeEnum_PokeType_Ice      PokeTypeEnum = 6
	PokeTypeEnum_PokeType_Fighting PokeTypeEnum = 7
	PokeTypeEnum_PokeType_Poison   PokeTypeEnum = 8
	PokeTypeEnum_PokeType_Ground   PokeTypeEnum = 9
	PokeTypeEnum_PokeType_Flying   PokeTypeEnum = 10
	PokeTypeEnum_PokeType_Psychic  PokeTypeEnum = 11
	PokeTypeEnum_PokeType_Bug      PokeTypeEnum = 12
	PokeTypeEnum_PokeType_Rock     PokeTypeEnum = 13
	PokeTypeEnum_PokeType_Ghost    PokeTypeEnum = 14
	PokeTypeEnum_PokeType_Dragon   PokeTypeEnum = 15
	PokeTypeEnum_PokeType_Dark     PokeTypeEnum = 16
	PokeTypeEnum_PokeType_Steel    PokeTypeEnum = 17
	PokeTypeEnum_PokeType_Fairy    PokeTypeEnum = 18
	PokeTypeEnum_PokeType_Stellar  PokeTypeEnum = 19
)

// Enum value maps for PokeTypeEnum.
var (
	PokeTypeEnum_name = map[int32]string{
		0:  "PokeType_Unknown",
		1:  "PokeType_Normal",
		2:  "PokeType_Fire",
		3:  "PokeType_Water",
		4:  "PokeType_Electric",
		5:  "PokeType_Grass",
		6:  "PokeType_Ice",
		7:  "PokeType_Fighting",
		8:  "PokeType_Poison",
		9:  "PokeType_Ground",
		10: "PokeType_Flying",
		11: "PokeType_Psychic",
		12: "PokeType_Bug",
		13: "PokeType_Rock",
		14: "PokeType_Ghost",
		15: "PokeType_Dragon",
		16: "PokeType_Dark",
		17: "PokeType_Steel",
		18: "PokeType_Fairy",
		19: "PokeType_Stellar",
	}
	PokeTypeEnum_value = map[string]int32{
		"PokeType_Unknown":  0,
		"PokeType_Normal":   1,
		"PokeType_Fire":     2,
		"PokeType_Water":    3,
		"PokeType_Electric": 4,
		"PokeType_Grass":    5,
		"PokeType_Ice":      6,
		"PokeType_Fighting": 7,
		"PokeType_Poison":   8,
		"PokeType_Ground":   9,
		"PokeType_Flying":   10,
		"PokeType_Psychic":  11,
		"PokeType_Bug":      12,
		"PokeType_Rock":     13,
		"PokeType_Ghost":    14,
		"PokeType_Dragon":   15,
		"PokeType_Dark":     16,
		"PokeType_Steel":    17,
		"PokeType_Fairy":    18,
		"PokeType_Stellar":  19,
	}
)

func (x PokeTypeEnum) Enum() *PokeTypeEnum {
	p := new(PokeTypeEnum)
	*p = x
	return p
}

func (x PokeTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[2].Descriptor()
}

func (PokeTypeEnum) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[2]
}

func (x PokeTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeTypeEnum.Descriptor instead.
func (PokeTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{2}
}

type Nature int32

const (
	Nature_NATURE_UNSPECIFIED Nature = 0  // 默认值
	Nature_ADAMANT            Nature = 1  // +atk, -spa
	Nature_BASHFUL            Nature = 2  // 无增减
	Nature_BOLD               Nature = 3  // +def, -atk
	Nature_BRAVE              Nature = 4  // +atk, -spe
	Nature_CALM               Nature = 5  // +spd, -atk
	Nature_CAREFUL            Nature = 6  // +spd, -spa
	Nature_DOCILE             Nature = 7  // 无增减
	Nature_GENTLE             Nature = 8  // +spd, -def
	Nature_HARDY              Nature = 9  // 无增减
	Nature_HASTY              Nature = 10 // +spe, -def
	Nature_IMPISH             Nature = 11 // +def, -spa
	Nature_JOLLY              Nature = 12 // +spe, -spa
	Nature_LAX                Nature = 13 // +def, -spd
	Nature_LONELY             Nature = 14 // +atk, -def
	Nature_MILD               Nature = 15 // +spa, -def
	Nature_MODEST             Nature = 16 // +spa, -atk
	Nature_NAIVE              Nature = 17 // +spe, -spd
	Nature_NAUGHTY            Nature = 18 // +atk, -spd
	Nature_QUIET              Nature = 19 // +spa, -spe
	Nature_QUIRKY             Nature = 20 // 无增减
	Nature_RASH               Nature = 21 // +spa, -spd
	Nature_RELAXED            Nature = 22 // +def, -spe
	Nature_SASSY              Nature = 23 // +spd, -spe
	Nature_SERIOUS            Nature = 24 // 无增减
	Nature_TIMID              Nature = 25 // +spe, -atk
)

// Enum value maps for Nature.
var (
	Nature_name = map[int32]string{
		0:  "NATURE_UNSPECIFIED",
		1:  "ADAMANT",
		2:  "BASHFUL",
		3:  "BOLD",
		4:  "BRAVE",
		5:  "CALM",
		6:  "CAREFUL",
		7:  "DOCILE",
		8:  "GENTLE",
		9:  "HARDY",
		10: "HASTY",
		11: "IMPISH",
		12: "JOLLY",
		13: "LAX",
		14: "LONELY",
		15: "MILD",
		16: "MODEST",
		17: "NAIVE",
		18: "NAUGHTY",
		19: "QUIET",
		20: "QUIRKY",
		21: "RASH",
		22: "RELAXED",
		23: "SASSY",
		24: "SERIOUS",
		25: "TIMID",
	}
	Nature_value = map[string]int32{
		"NATURE_UNSPECIFIED": 0,
		"ADAMANT":            1,
		"BASHFUL":            2,
		"BOLD":               3,
		"BRAVE":              4,
		"CALM":               5,
		"CAREFUL":            6,
		"DOCILE":             7,
		"GENTLE":             8,
		"HARDY":              9,
		"HASTY":              10,
		"IMPISH":             11,
		"JOLLY":              12,
		"LAX":                13,
		"LONELY":             14,
		"MILD":               15,
		"MODEST":             16,
		"NAIVE":              17,
		"NAUGHTY":            18,
		"QUIET":              19,
		"QUIRKY":             20,
		"RASH":               21,
		"RELAXED":            22,
		"SASSY":              23,
		"SERIOUS":            24,
		"TIMID":              25,
	}
)

func (x Nature) Enum() *Nature {
	p := new(Nature)
	*p = x
	return p
}

func (x Nature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Nature) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[3].Descriptor()
}

func (Nature) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[3]
}

func (x Nature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Nature.Descriptor instead.
func (Nature) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{3}
}

type Gender int32

const (
	Gender_GenderNull Gender = 0
	Gender_M          Gender = 1
	Gender_F          Gender = 2
	Gender_N          Gender = 3
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "GenderNull",
		1: "M",
		2: "F",
		3: "N",
	}
	Gender_value = map[string]int32{
		"GenderNull": 0,
		"M":          1,
		"F":          2,
		"N":          3,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[4].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[4]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{4}
}

type PokemonStatKey int32

const (
	PokemonStatKey_StatKey_UNSPECIFIED    PokemonStatKey = 0
	PokemonStatKey_StatKey_HP             PokemonStatKey = 1
	PokemonStatKey_StatKey_Attack         PokemonStatKey = 2
	PokemonStatKey_StatKey_Defense        PokemonStatKey = 3
	PokemonStatKey_StatKey_SpecialAttack  PokemonStatKey = 4
	PokemonStatKey_StatKey_SpecialDefense PokemonStatKey = 5
	PokemonStatKey_StatKey_Speed          PokemonStatKey = 6
	PokemonStatKey_StatKey_Accuracy       PokemonStatKey = 7
	PokemonStatKey_StatKey_Evasion        PokemonStatKey = 8
)

// Enum value maps for PokemonStatKey.
var (
	PokemonStatKey_name = map[int32]string{
		0: "StatKey_UNSPECIFIED",
		1: "StatKey_HP",
		2: "StatKey_Attack",
		3: "StatKey_Defense",
		4: "StatKey_SpecialAttack",
		5: "StatKey_SpecialDefense",
		6: "StatKey_Speed",
		7: "StatKey_Accuracy",
		8: "StatKey_Evasion",
	}
	PokemonStatKey_value = map[string]int32{
		"StatKey_UNSPECIFIED":    0,
		"StatKey_HP":             1,
		"StatKey_Attack":         2,
		"StatKey_Defense":        3,
		"StatKey_SpecialAttack":  4,
		"StatKey_SpecialDefense": 5,
		"StatKey_Speed":          6,
		"StatKey_Accuracy":       7,
		"StatKey_Evasion":        8,
	}
)

func (x PokemonStatKey) Enum() *PokemonStatKey {
	p := new(PokemonStatKey)
	*p = x
	return p
}

func (x PokemonStatKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokemonStatKey) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[5].Descriptor()
}

func (PokemonStatKey) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[5]
}

func (x PokemonStatKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokemonStatKey.Descriptor instead.
func (PokemonStatKey) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{5}
}

type PokeCateLevel int32

const (
	PokeCateLevel_PokeCateLevel_Unknown          PokeCateLevel = 0
	PokeCateLevel_PokeCateLevel_1                PokeCateLevel = 1
	PokeCateLevel_PokeCateLevel_2                PokeCateLevel = 2
	PokeCateLevel_PokeCateLevel_3                PokeCateLevel = 3
	PokeCateLevel_PokeCateLevel_4                PokeCateLevel = 4
	PokeCateLevel_PokeCateLevel_5                PokeCateLevel = 5
	PokeCateLevel_PokeCateLevel_First            PokeCateLevel = 6  //预三家
	PokeCateLevel_PokeCateLevel_Late             PokeCateLevel = 7  //大器晚成
	PokeCateLevel_PokeCateLevel_Ultra            PokeCateLevel = 8  //究极异兽
	PokeCateLevel_PokeCateLevel_Paradox          PokeCateLevel = 9  //悖谬
	PokeCateLevel_PokeCateLevel_LegendaryNormal  PokeCateLevel = 10 //传说宝可梦（规则允许
	PokeCateLevel_PokeCateLevel_LegendarySpecial PokeCateLevel = 11 //传说宝可梦（规则禁止
	PokeCateLevel_PokeCateLevel_Mythical         PokeCateLevel = 12 //幻之宝可梦
	PokeCateLevel_PokeCateLevel_Special          PokeCateLevel = 13 //特殊（百变怪
)

// Enum value maps for PokeCateLevel.
var (
	PokeCateLevel_name = map[int32]string{
		0:  "PokeCateLevel_Unknown",
		1:  "PokeCateLevel_1",
		2:  "PokeCateLevel_2",
		3:  "PokeCateLevel_3",
		4:  "PokeCateLevel_4",
		5:  "PokeCateLevel_5",
		6:  "PokeCateLevel_First",
		7:  "PokeCateLevel_Late",
		8:  "PokeCateLevel_Ultra",
		9:  "PokeCateLevel_Paradox",
		10: "PokeCateLevel_LegendaryNormal",
		11: "PokeCateLevel_LegendarySpecial",
		12: "PokeCateLevel_Mythical",
		13: "PokeCateLevel_Special",
	}
	PokeCateLevel_value = map[string]int32{
		"PokeCateLevel_Unknown":          0,
		"PokeCateLevel_1":                1,
		"PokeCateLevel_2":                2,
		"PokeCateLevel_3":                3,
		"PokeCateLevel_4":                4,
		"PokeCateLevel_5":                5,
		"PokeCateLevel_First":            6,
		"PokeCateLevel_Late":             7,
		"PokeCateLevel_Ultra":            8,
		"PokeCateLevel_Paradox":          9,
		"PokeCateLevel_LegendaryNormal":  10,
		"PokeCateLevel_LegendarySpecial": 11,
		"PokeCateLevel_Mythical":         12,
		"PokeCateLevel_Special":          13,
	}
)

func (x PokeCateLevel) Enum() *PokeCateLevel {
	p := new(PokeCateLevel)
	*p = x
	return p
}

func (x PokeCateLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PokeCateLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_MainServer_Poke_proto_enumTypes[6].Descriptor()
}

func (PokeCateLevel) Type() protoreflect.EnumType {
	return &file_MainServer_Poke_proto_enumTypes[6]
}

func (x PokeCateLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PokeCateLevel.Descriptor instead.
func (PokeCateLevel) EnumDescriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{6}
}

type Poke struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Tid             int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                                                 // user 信息
	Name            string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                // 宝可梦名称
	NickName        string                 `protobuf:"bytes,4,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`                                                        // 宝可梦昵称
	BallName        string                 `protobuf:"bytes,5,opt,name=ball_name,json=ballName,proto3" json:"ball_name,omitempty"`                                                        // 捕获使用的球
	ItemInfo        *PokeItemInfo          `protobuf:"bytes,6,opt,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`                                                        // 携带的道具名称 //专属训练师的道具加个后缀_only
	Ability         string                 `protobuf:"bytes,7,opt,name=ability,proto3" json:"ability,omitempty"`                                                                          // 特性
	Evs             *PokeStat              `protobuf:"bytes,8,opt,name=evs,proto3" json:"evs,omitempty"`                                                                                  // 宝可梦的努力值
	Ivs             *PokeStat              `protobuf:"bytes,9,opt,name=ivs,proto3" json:"ivs,omitempty"`                                                                                  // 宝可梦的个体值
	TransactionType TransactionType        `protobuf:"varint,10,opt,name=transaction_type,json=transactionType,proto3,enum=MainServer.TransactionType" json:"transaction_type,omitempty"` // 是否在售或者在出租
	SaleInfo        *SaleInfo              `protobuf:"bytes,11,opt,name=sale_info,json=saleInfo,proto3" json:"sale_info,omitempty"`                                                       // 售价
	// string stats = 12;  // 宝可梦当前状态
	BorrowInfo    *PokeBorrowInfo   `protobuf:"bytes,12,opt,name=borrow_info,json=borrowInfo,proto3" json:"borrow_info,omitempty"` // 宝可梦当前状态
	Moves         []*PokeSimpleMove `protobuf:"bytes,13,rep,name=moves,proto3" json:"moves,omitempty"`                             // 宝可梦的招式
	Level         int32             `protobuf:"varint,14,opt,name=level,proto3" json:"level,omitempty"`                            // 等级
	Nature        Nature            `protobuf:"varint,15,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"`   // 宝可梦性格信息
	Status        *PokeStatusInfo   `protobuf:"bytes,16,opt,name=status,proto3" json:"status,omitempty"`                           // 宝可梦状态信息
	Experience    int64             `protobuf:"varint,17,opt,name=experience,proto3" json:"experience,omitempty"`                  // 经验值
	Born          *BornInfo         `protobuf:"bytes,18,opt,name=born,proto3" json:"born,omitempty"`                               // 出生信息
	Egg           bool              `protobuf:"varint,19,opt,name=egg,proto3" json:"egg,omitempty"`                                // 是否为蛋
	Shiny         int32             `protobuf:"varint,20,opt,name=shiny,proto3" json:"shiny,omitempty"`                            // 是否为异色（0为普通）
	Gender        Gender            `protobuf:"varint,21,opt,name=gender,proto3,enum=MainServer.Gender" json:"gender,omitempty"`   // 性别
	HpSub         int32             `protobuf:"varint,22,opt,name=hp_sub,json=hpSub,proto3" json:"hp_sub,omitempty"`               // hp
	Happiness     int32             `protobuf:"varint,23,opt,name=happiness,proto3" json:"happiness,omitempty"`
	SysExtra      *PokeSysExtra     `protobuf:"bytes,24,opt,name=sys_extra,json=sysExtra,proto3" json:"sys_extra,omitempty"`
	Extra         *PokeExtra        `protobuf:"bytes,25,opt,name=extra,proto3" json:"extra,omitempty"` // 额外信息
	Release       bool              `protobuf:"varint,26,opt,name=release,proto3" json:"release,omitempty"`
	HonorInfo     *HonorInfo        `protobuf:"bytes,27,opt,name=honorInfo,proto3" json:"honorInfo,omitempty"`
	BreedCount    int32             `protobuf:"varint,28,opt,name=breedCount,proto3" json:"breedCount,omitempty"`
	CreateTs      int64             `protobuf:"varint,29,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"` // 创建时间戳
	UpdateTs      int64             `protobuf:"varint,30,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间戳
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Poke) Reset() {
	*x = Poke{}
	mi := &file_MainServer_Poke_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Poke) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Poke) ProtoMessage() {}

func (x *Poke) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Poke.ProtoReflect.Descriptor instead.
func (*Poke) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{0}
}

func (x *Poke) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Poke) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *Poke) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Poke) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *Poke) GetBallName() string {
	if x != nil {
		return x.BallName
	}
	return ""
}

func (x *Poke) GetItemInfo() *PokeItemInfo {
	if x != nil {
		return x.ItemInfo
	}
	return nil
}

func (x *Poke) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *Poke) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

func (x *Poke) GetIvs() *PokeStat {
	if x != nil {
		return x.Ivs
	}
	return nil
}

func (x *Poke) GetTransactionType() TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return TransactionType_TransactionType_None
}

func (x *Poke) GetSaleInfo() *SaleInfo {
	if x != nil {
		return x.SaleInfo
	}
	return nil
}

func (x *Poke) GetBorrowInfo() *PokeBorrowInfo {
	if x != nil {
		return x.BorrowInfo
	}
	return nil
}

func (x *Poke) GetMoves() []*PokeSimpleMove {
	if x != nil {
		return x.Moves
	}
	return nil
}

func (x *Poke) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Poke) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

func (x *Poke) GetStatus() *PokeStatusInfo {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *Poke) GetExperience() int64 {
	if x != nil {
		return x.Experience
	}
	return 0
}

func (x *Poke) GetBorn() *BornInfo {
	if x != nil {
		return x.Born
	}
	return nil
}

func (x *Poke) GetEgg() bool {
	if x != nil {
		return x.Egg
	}
	return false
}

func (x *Poke) GetShiny() int32 {
	if x != nil {
		return x.Shiny
	}
	return 0
}

func (x *Poke) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderNull
}

func (x *Poke) GetHpSub() int32 {
	if x != nil {
		return x.HpSub
	}
	return 0
}

func (x *Poke) GetHappiness() int32 {
	if x != nil {
		return x.Happiness
	}
	return 0
}

func (x *Poke) GetSysExtra() *PokeSysExtra {
	if x != nil {
		return x.SysExtra
	}
	return nil
}

func (x *Poke) GetExtra() *PokeExtra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Poke) GetRelease() bool {
	if x != nil {
		return x.Release
	}
	return false
}

func (x *Poke) GetHonorInfo() *HonorInfo {
	if x != nil {
		return x.HonorInfo
	}
	return nil
}

func (x *Poke) GetBreedCount() int32 {
	if x != nil {
		return x.BreedCount
	}
	return 0
}

func (x *Poke) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *Poke) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

type PokeItemInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	InventoryId   int64                  `protobuf:"varint,1,opt,name=inventory_id,json=inventoryId,proto3" json:"inventory_id,omitempty"`                                   // 库存ID
	Tid           int64                  `protobuf:"varint,2,opt,name=tid,proto3" json:"tid,omitempty"`                                                                      // 训练师ID
	ItemName      string                 `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`                                             // 道具ID（道具名称）
	Quantity      int32                  `protobuf:"varint,4,opt,name=quantity,proto3" json:"quantity,omitempty"`                                                            // 数量
	ItemSaleType  ItemSaleType           `protobuf:"varint,5,opt,name=item_sale_type,json=itemSaleType,proto3,enum=MainServer.ItemSaleType" json:"item_sale_type,omitempty"` // 道具购买渠道类型
	TeamType      TrainerTeam            `protobuf:"varint,6,opt,name=team_type,json=teamType,proto3,enum=MainServer.TrainerTeam" json:"team_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeItemInfo) Reset() {
	*x = PokeItemInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeItemInfo) ProtoMessage() {}

func (x *PokeItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeItemInfo.ProtoReflect.Descriptor instead.
func (*PokeItemInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{1}
}

func (x *PokeItemInfo) GetInventoryId() int64 {
	if x != nil {
		return x.InventoryId
	}
	return 0
}

func (x *PokeItemInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *PokeItemInfo) GetItemName() string {
	if x != nil {
		return x.ItemName
	}
	return ""
}

func (x *PokeItemInfo) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *PokeItemInfo) GetItemSaleType() ItemSaleType {
	if x != nil {
		return x.ItemSaleType
	}
	return ItemSaleType_ItemSaleType_Normal
}

func (x *PokeItemInfo) GetTeamType() TrainerTeam {
	if x != nil {
		return x.TeamType
	}
	return TrainerTeam_TRAINER_TEAM_NONE
}

type PokeStatusInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LiveStatus    PokeLiveStatus         `protobuf:"varint,1,opt,name=live_status,json=liveStatus,proto3,enum=MainServer.PokeLiveStatus" json:"live_status,omitempty"` // 生命状态
	IsOnly        bool                   `protobuf:"varint,2,opt,name=is_only,json=isOnly,proto3" json:"is_only,omitempty"`                                            // 是否为专属训练师的宝可梦
	Sealed        string                 `protobuf:"bytes,3,opt,name=sealed,proto3" json:"sealed,omitempty"`                                                           // 密封
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeStatusInfo) Reset() {
	*x = PokeStatusInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeStatusInfo) ProtoMessage() {}

func (x *PokeStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeStatusInfo.ProtoReflect.Descriptor instead.
func (*PokeStatusInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{2}
}

func (x *PokeStatusInfo) GetLiveStatus() PokeLiveStatus {
	if x != nil {
		return x.LiveStatus
	}
	return PokeLiveStatus_PokeLiveStatus_Normal
}

func (x *PokeStatusInfo) GetIsOnly() bool {
	if x != nil {
		return x.IsOnly
	}
	return false
}

func (x *PokeStatusInfo) GetSealed() string {
	if x != nil {
		return x.Sealed
	}
	return ""
}

type PokeBorrowInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BorrowTs        int64                  `protobuf:"varint,1,opt,name=borrow_ts,json=borrowTs,proto3" json:"borrow_ts,omitempty"`                        // 借用时间
	ReturnTs        int64                  `protobuf:"varint,2,opt,name=return_ts,json=returnTs,proto3" json:"return_ts,omitempty"`                        // 归还时间
	CanRenewed      bool                   `protobuf:"varint,3,opt,name=can_renewed,json=canRenewed,proto3" json:"can_renewed,omitempty"`                  // 是否可续借（最后3天可以续）
	BorrowTrainerId int64                  `protobuf:"varint,4,opt,name=borrow_trainer_id,json=borrowTrainerId,proto3" json:"borrow_trainer_id,omitempty"` //租借的训练师
	DepositCoin     int64                  `protobuf:"varint,5,opt,name=deposit_coin,json=depositCoin,proto3" json:"deposit_coin,omitempty"`               //押金（每过一天可以提取一次，提取一次扣除一次）（可能续借的时候涨价，还有当面租借）
	Price           int32                  `protobuf:"varint,6,opt,name=price,proto3" json:"price,omitempty"`                                              //当前借用的价格，（假如已经续约，那么下一次刷新是该宝可梦被读取后 TODO
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PokeBorrowInfo) Reset() {
	*x = PokeBorrowInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBorrowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBorrowInfo) ProtoMessage() {}

func (x *PokeBorrowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBorrowInfo.ProtoReflect.Descriptor instead.
func (*PokeBorrowInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{3}
}

func (x *PokeBorrowInfo) GetBorrowTs() int64 {
	if x != nil {
		return x.BorrowTs
	}
	return 0
}

func (x *PokeBorrowInfo) GetReturnTs() int64 {
	if x != nil {
		return x.ReturnTs
	}
	return 0
}

func (x *PokeBorrowInfo) GetCanRenewed() bool {
	if x != nil {
		return x.CanRenewed
	}
	return false
}

func (x *PokeBorrowInfo) GetBorrowTrainerId() int64 {
	if x != nil {
		return x.BorrowTrainerId
	}
	return 0
}

func (x *PokeBorrowInfo) GetDepositCoin() int64 {
	if x != nil {
		return x.DepositCoin
	}
	return 0
}

func (x *PokeBorrowInfo) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

type HonorInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Badges        []string               `protobuf:"bytes,1,rep,name=badges,proto3" json:"badges,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HonorInfo) Reset() {
	*x = HonorInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HonorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HonorInfo) ProtoMessage() {}

func (x *HonorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HonorInfo.ProtoReflect.Descriptor instead.
func (*HonorInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{4}
}

func (x *HonorInfo) GetBadges() []string {
	if x != nil {
		return x.Badges
	}
	return nil
}

type SaleInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Price         int32                  `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	SpecialCoin   int32                  `protobuf:"varint,2,opt,name=special_coin,json=specialCoin,proto3" json:"special_coin,omitempty"`
	CreateTs      int64                  `protobuf:"varint,3,opt,name=create_ts,json=createTs,proto3" json:"create_ts,omitempty"`          // 上架时间
	RentDay       int32                  `protobuf:"varint,4,opt,name=rent_day,json=rentDay,proto3" json:"rent_day,omitempty"`             //租借时间
	Renewed       bool                   `protobuf:"varint,5,opt,name=renewed,proto3" json:"renewed,omitempty"`                            //已经续约（无法再修改续约条件
	DepositCoin   int32                  `protobuf:"varint,6,opt,name=deposit_coin,json=depositCoin,proto3" json:"deposit_coin,omitempty"` //这个是卖方的押金，假如出租的时候毁约需要的赔偿 （出租结束可以全部返回，更改价格需要重新计算押金
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaleInfo) Reset() {
	*x = SaleInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaleInfo) ProtoMessage() {}

func (x *SaleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaleInfo.ProtoReflect.Descriptor instead.
func (*SaleInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{5}
}

func (x *SaleInfo) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *SaleInfo) GetSpecialCoin() int32 {
	if x != nil {
		return x.SpecialCoin
	}
	return 0
}

func (x *SaleInfo) GetCreateTs() int64 {
	if x != nil {
		return x.CreateTs
	}
	return 0
}

func (x *SaleInfo) GetRentDay() int32 {
	if x != nil {
		return x.RentDay
	}
	return 0
}

func (x *SaleInfo) GetRenewed() bool {
	if x != nil {
		return x.Renewed
	}
	return false
}

func (x *SaleInfo) GetDepositCoin() int32 {
	if x != nil {
		return x.DepositCoin
	}
	return 0
}

type PokeStat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hp            int32                  `protobuf:"varint,1,opt,name=hp,proto3" json:"hp,omitempty"`
	Atk           int32                  `protobuf:"varint,2,opt,name=atk,proto3" json:"atk,omitempty"`
	Def           int32                  `protobuf:"varint,3,opt,name=def,proto3" json:"def,omitempty"`
	Spa           int32                  `protobuf:"varint,4,opt,name=spa,proto3" json:"spa,omitempty"`
	Spd           int32                  `protobuf:"varint,5,opt,name=spd,proto3" json:"spd,omitempty"`
	Spe           int32                  `protobuf:"varint,6,opt,name=spe,proto3" json:"spe,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeStat) Reset() {
	*x = PokeStat{}
	mi := &file_MainServer_Poke_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeStat) ProtoMessage() {}

func (x *PokeStat) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeStat.ProtoReflect.Descriptor instead.
func (*PokeStat) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{6}
}

func (x *PokeStat) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PokeStat) GetAtk() int32 {
	if x != nil {
		return x.Atk
	}
	return 0
}

func (x *PokeStat) GetDef() int32 {
	if x != nil {
		return x.Def
	}
	return 0
}

func (x *PokeStat) GetSpa() int32 {
	if x != nil {
		return x.Spa
	}
	return 0
}

func (x *PokeStat) GetSpd() int32 {
	if x != nil {
		return x.Spd
	}
	return 0
}

func (x *PokeStat) GetSpe() int32 {
	if x != nil {
		return x.Spe
	}
	return 0
}

type PokeBoostStat struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Hp             int32                  `protobuf:"varint,1,opt,name=hp,proto3" json:"hp,omitempty"`
	Attack         int32                  `protobuf:"varint,2,opt,name=attack,proto3" json:"attack,omitempty"`
	Defense        int32                  `protobuf:"varint,3,opt,name=defense,proto3" json:"defense,omitempty"`
	SpecialAttack  int32                  `protobuf:"varint,4,opt,name=specialAttack,proto3" json:"specialAttack,omitempty"`
	SpecialDefense int32                  `protobuf:"varint,5,opt,name=specialDefense,proto3" json:"specialDefense,omitempty"`
	Speed          int32                  `protobuf:"varint,6,opt,name=speed,proto3" json:"speed,omitempty"`
	Accuracy       int32                  `protobuf:"varint,7,opt,name=accuracy,proto3" json:"accuracy,omitempty"`
	Evasion        int32                  `protobuf:"varint,8,opt,name=evasion,proto3" json:"evasion,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PokeBoostStat) Reset() {
	*x = PokeBoostStat{}
	mi := &file_MainServer_Poke_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeBoostStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeBoostStat) ProtoMessage() {}

func (x *PokeBoostStat) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeBoostStat.ProtoReflect.Descriptor instead.
func (*PokeBoostStat) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{7}
}

func (x *PokeBoostStat) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *PokeBoostStat) GetAttack() int32 {
	if x != nil {
		return x.Attack
	}
	return 0
}

func (x *PokeBoostStat) GetDefense() int32 {
	if x != nil {
		return x.Defense
	}
	return 0
}

func (x *PokeBoostStat) GetSpecialAttack() int32 {
	if x != nil {
		return x.SpecialAttack
	}
	return 0
}

func (x *PokeBoostStat) GetSpecialDefense() int32 {
	if x != nil {
		return x.SpecialDefense
	}
	return 0
}

func (x *PokeBoostStat) GetSpeed() int32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

func (x *PokeBoostStat) GetAccuracy() int32 {
	if x != nil {
		return x.Accuracy
	}
	return 0
}

func (x *PokeBoostStat) GetEvasion() int32 {
	if x != nil {
		return x.Evasion
	}
	return 0
}

type PokeSimpleMove struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	PpPro         int32                  `protobuf:"varint,2,opt,name=pp_pro,json=ppPro,proto3" json:"pp_pro,omitempty"`
	PpSub         int32                  `protobuf:"varint,3,opt,name=pp_sub,json=ppSub,proto3" json:"pp_sub,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeSimpleMove) Reset() {
	*x = PokeSimpleMove{}
	mi := &file_MainServer_Poke_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeSimpleMove) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeSimpleMove) ProtoMessage() {}

func (x *PokeSimpleMove) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeSimpleMove.ProtoReflect.Descriptor instead.
func (*PokeSimpleMove) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{8}
}

func (x *PokeSimpleMove) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PokeSimpleMove) GetPpPro() int32 {
	if x != nil {
		return x.PpPro
	}
	return 0
}

func (x *PokeSimpleMove) GetPpSub() int32 {
	if x != nil {
		return x.PpSub
	}
	return 0
}

type BornInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	BornType         BornInfoType           `protobuf:"varint,1,opt,name=born_type,json=bornType,proto3,enum=MainServer.BornInfoType" json:"born_type,omitempty"`
	BornTs           int64                  `protobuf:"varint,2,opt,name=born_ts,json=bornTs,proto3" json:"born_ts,omitempty"`
	Tid              int64                  `protobuf:"varint,3,opt,name=tid,proto3" json:"tid,omitempty"`                                   //召唤的和第一个训练师意思不一样 （召唤后再次对战的时候，需要用到这个
	TrainerName      string                 `protobuf:"bytes,4,opt,name=trainer_name,json=trainerName,proto3" json:"trainer_name,omitempty"` //召唤的和第一个训练师意思不一样 （召唤后再次对战的时候，需要用到这个
	FristTrainerName string                 `protobuf:"bytes,5,opt,name=frist_trainer_name,json=fristTrainerName,proto3" json:"frist_trainer_name,omitempty"`
	IsBattle         bool                   `protobuf:"varint,6,opt,name=is_battle,json=isBattle,proto3" json:"is_battle,omitempty"` //是否已经战斗过
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BornInfo) Reset() {
	*x = BornInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BornInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BornInfo) ProtoMessage() {}

func (x *BornInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BornInfo.ProtoReflect.Descriptor instead.
func (*BornInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{9}
}

func (x *BornInfo) GetBornType() BornInfoType {
	if x != nil {
		return x.BornType
	}
	return BornInfoType_BornInfoType_Wild
}

func (x *BornInfo) GetBornTs() int64 {
	if x != nil {
		return x.BornTs
	}
	return 0
}

func (x *BornInfo) GetTid() int64 {
	if x != nil {
		return x.Tid
	}
	return 0
}

func (x *BornInfo) GetTrainerName() string {
	if x != nil {
		return x.TrainerName
	}
	return ""
}

func (x *BornInfo) GetFristTrainerName() string {
	if x != nil {
		return x.FristTrainerName
	}
	return ""
}

func (x *BornInfo) GetIsBattle() bool {
	if x != nil {
		return x.IsBattle
	}
	return false
}

type PokeSysExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DynamaxLevel  int32                  `protobuf:"varint,1,opt,name=dynamaxLevel,proto3" json:"dynamaxLevel,omitempty"`
	Gigantamax    bool                   `protobuf:"varint,2,opt,name=gigantamax,proto3" json:"gigantamax,omitempty"`
	Terastal      string                 `protobuf:"bytes,3,opt,name=terastal,proto3" json:"terastal,omitempty"` //太晶化类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeSysExtra) Reset() {
	*x = PokeSysExtra{}
	mi := &file_MainServer_Poke_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeSysExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeSysExtra) ProtoMessage() {}

func (x *PokeSysExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeSysExtra.ProtoReflect.Descriptor instead.
func (*PokeSysExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{10}
}

func (x *PokeSysExtra) GetDynamaxLevel() int32 {
	if x != nil {
		return x.DynamaxLevel
	}
	return 0
}

func (x *PokeSysExtra) GetGigantamax() bool {
	if x != nil {
		return x.Gigantamax
	}
	return false
}

func (x *PokeSysExtra) GetTerastal() string {
	if x != nil {
		return x.Terastal
	}
	return ""
}

type PokeExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TempItem      string                 `protobuf:"bytes,1,opt,name=tempItem,proto3" json:"tempItem,omitempty"`       //临时道具 //用于租借方更换道具 //归还后退还道具
	TempEvs       *PokeStat              `protobuf:"bytes,2,opt,name=tempEvs,proto3" json:"tempEvs,omitempty"`         // 临时宝可梦的努力值 //用于租借修改努力值 //待定使用
	LastExpInfo   *PokeExpInfo           `protobuf:"bytes,3,opt,name=lastExpInfo,proto3" json:"lastExpInfo,omitempty"` //上次升级信息(也可能是进化)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeExtra) Reset() {
	*x = PokeExtra{}
	mi := &file_MainServer_Poke_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeExtra) ProtoMessage() {}

func (x *PokeExtra) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeExtra.ProtoReflect.Descriptor instead.
func (*PokeExtra) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{11}
}

func (x *PokeExtra) GetTempItem() string {
	if x != nil {
		return x.TempItem
	}
	return ""
}

func (x *PokeExtra) GetTempEvs() *PokeStat {
	if x != nil {
		return x.TempEvs
	}
	return nil
}

func (x *PokeExtra) GetLastExpInfo() *PokeExpInfo {
	if x != nil {
		return x.LastExpInfo
	}
	return nil
}

//	message PokeLastLevelUpInfo {
//	    int64 ts = 1;
//	    int32 level = 2;
//	    int32 exp = 3;
//	    bool isEvolution = 4;
//	    repeated string moves = 5;
//	}
type LastExpInfoRequest struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	PokeId      int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	LastExpInfo *PokeExpInfo           `protobuf:"bytes,2,opt,name=lastExpInfo,proto3" json:"lastExpInfo,omitempty"`
	// repeated PokeSimpleMove moves = 3;
	Moves         []string `protobuf:"bytes,3,rep,name=moves,proto3" json:"moves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LastExpInfoRequest) Reset() {
	*x = LastExpInfoRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LastExpInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LastExpInfoRequest) ProtoMessage() {}

func (x *LastExpInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LastExpInfoRequest.ProtoReflect.Descriptor instead.
func (*LastExpInfoRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{12}
}

func (x *LastExpInfoRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *LastExpInfoRequest) GetLastExpInfo() *PokeExpInfo {
	if x != nil {
		return x.LastExpInfo
	}
	return nil
}

func (x *LastExpInfoRequest) GetMoves() []string {
	if x != nil {
		return x.Moves
	}
	return nil
}

type RpcPokeUpdateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUpdateRequest) Reset() {
	*x = RpcPokeUpdateRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUpdateRequest) ProtoMessage() {}

func (x *RpcPokeUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUpdateRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeUpdateRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{13}
}

type RpcPokeUpdateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUpdateResponse) Reset() {
	*x = RpcPokeUpdateResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUpdateResponse) ProtoMessage() {}

func (x *RpcPokeUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUpdateResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeUpdateResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{14}
}

type RpcPokeChangeEvsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Evs           *PokeStat              `protobuf:"bytes,2,opt,name=evs,proto3" json:"evs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeEvsRequest) Reset() {
	*x = RpcPokeChangeEvsRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeEvsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeEvsRequest) ProtoMessage() {}

func (x *RpcPokeChangeEvsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeEvsRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeEvsRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{15}
}

func (x *RpcPokeChangeEvsRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeEvsRequest) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

type RpcPokeChangeEvsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Evs           *PokeStat              `protobuf:"bytes,2,opt,name=evs,proto3" json:"evs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeEvsResponse) Reset() {
	*x = RpcPokeChangeEvsResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeEvsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeEvsResponse) ProtoMessage() {}

func (x *RpcPokeChangeEvsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeEvsResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeEvsResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{16}
}

func (x *RpcPokeChangeEvsResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeEvsResponse) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

type RpcPokeChangeAbilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Ability       string                 `protobuf:"bytes,2,opt,name=ability,proto3" json:"ability,omitempty"`
	UseCount      int32                  `protobuf:"varint,3,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeAbilityRequest) Reset() {
	*x = RpcPokeChangeAbilityRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeAbilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeAbilityRequest) ProtoMessage() {}

func (x *RpcPokeChangeAbilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeAbilityRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeAbilityRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{17}
}

func (x *RpcPokeChangeAbilityRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeAbilityRequest) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *RpcPokeChangeAbilityRequest) GetUseCount() int32 {
	if x != nil {
		return x.UseCount
	}
	return 0
}

type RpcPokeChangeAbilityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeAbilityResponse) Reset() {
	*x = RpcPokeChangeAbilityResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeAbilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeAbilityResponse) ProtoMessage() {}

func (x *RpcPokeChangeAbilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeAbilityResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeAbilityResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{18}
}

func (x *RpcPokeChangeAbilityResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeAbilityResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type RpcPokeChangeMoveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	MoveName      string                 `protobuf:"bytes,2,opt,name=move_name,json=moveName,proto3" json:"move_name,omitempty"`
	MoveIndex     int32                  `protobuf:"varint,3,opt,name=move_index,json=moveIndex,proto3" json:"move_index,omitempty"`
	IsRemove      bool                   `protobuf:"varint,4,opt,name=is_remove,json=isRemove,proto3" json:"is_remove,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeMoveRequest) Reset() {
	*x = RpcPokeChangeMoveRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeMoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeMoveRequest) ProtoMessage() {}

func (x *RpcPokeChangeMoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeMoveRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeMoveRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{19}
}

func (x *RpcPokeChangeMoveRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeMoveRequest) GetMoveName() string {
	if x != nil {
		return x.MoveName
	}
	return ""
}

func (x *RpcPokeChangeMoveRequest) GetMoveIndex() int32 {
	if x != nil {
		return x.MoveIndex
	}
	return 0
}

func (x *RpcPokeChangeMoveRequest) GetIsRemove() bool {
	if x != nil {
		return x.IsRemove
	}
	return false
}

type RpcPokeChangeMoveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Moves         []*PokeSimpleMove      `protobuf:"bytes,3,rep,name=moves,proto3" json:"moves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeMoveResponse) Reset() {
	*x = RpcPokeChangeMoveResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeMoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeMoveResponse) ProtoMessage() {}

func (x *RpcPokeChangeMoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeMoveResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeMoveResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{20}
}

func (x *RpcPokeChangeMoveResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeMoveResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcPokeChangeMoveResponse) GetMoves() []*PokeSimpleMove {
	if x != nil {
		return x.Moves
	}
	return nil
}

type RpcPokeUseCapRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	CapName       string                 `protobuf:"bytes,2,opt,name=cap_name,json=capName,proto3" json:"cap_name,omitempty"`
	StatKey       PokemonStatKey         `protobuf:"varint,3,opt,name=stat_key,json=statKey,proto3,enum=MainServer.PokemonStatKey" json:"stat_key,omitempty"`
	UseCount      int32                  `protobuf:"varint,4,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUseCapRequest) Reset() {
	*x = RpcPokeUseCapRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUseCapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUseCapRequest) ProtoMessage() {}

func (x *RpcPokeUseCapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUseCapRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeUseCapRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{21}
}

func (x *RpcPokeUseCapRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeUseCapRequest) GetCapName() string {
	if x != nil {
		return x.CapName
	}
	return ""
}

func (x *RpcPokeUseCapRequest) GetStatKey() PokemonStatKey {
	if x != nil {
		return x.StatKey
	}
	return PokemonStatKey_StatKey_UNSPECIFIED
}

func (x *RpcPokeUseCapRequest) GetUseCount() int32 {
	if x != nil {
		return x.UseCount
	}
	return 0
}

type RpcPokeUseCapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	UseResult     int32                  `protobuf:"varint,2,opt,name=use_result,json=useResult,proto3" json:"use_result,omitempty"`
	StatKey       PokemonStatKey         `protobuf:"varint,3,opt,name=stat_key,json=statKey,proto3,enum=MainServer.PokemonStatKey" json:"stat_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeUseCapResponse) Reset() {
	*x = RpcPokeUseCapResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeUseCapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeUseCapResponse) ProtoMessage() {}

func (x *RpcPokeUseCapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeUseCapResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeUseCapResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{22}
}

func (x *RpcPokeUseCapResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeUseCapResponse) GetUseResult() int32 {
	if x != nil {
		return x.UseResult
	}
	return 0
}

func (x *RpcPokeUseCapResponse) GetStatKey() PokemonStatKey {
	if x != nil {
		return x.StatKey
	}
	return PokemonStatKey_StatKey_UNSPECIFIED
}

type RpcPokeChangeNatureRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Nature        Nature                 `protobuf:"varint,2,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"`
	UseCount      int32                  `protobuf:"varint,3,opt,name=use_count,json=useCount,proto3" json:"use_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeNatureRequest) Reset() {
	*x = RpcPokeChangeNatureRequest{}
	mi := &file_MainServer_Poke_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeNatureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeNatureRequest) ProtoMessage() {}

func (x *RpcPokeChangeNatureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeNatureRequest.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeNatureRequest) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{23}
}

func (x *RpcPokeChangeNatureRequest) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeNatureRequest) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

func (x *RpcPokeChangeNatureRequest) GetUseCount() int32 {
	if x != nil {
		return x.UseCount
	}
	return 0
}

type RpcPokeChangeNatureResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PokeId        int64                  `protobuf:"varint,1,opt,name=poke_id,json=pokeId,proto3" json:"poke_id,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Nature        Nature                 `protobuf:"varint,3,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcPokeChangeNatureResponse) Reset() {
	*x = RpcPokeChangeNatureResponse{}
	mi := &file_MainServer_Poke_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcPokeChangeNatureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcPokeChangeNatureResponse) ProtoMessage() {}

func (x *RpcPokeChangeNatureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcPokeChangeNatureResponse.ProtoReflect.Descriptor instead.
func (*RpcPokeChangeNatureResponse) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{24}
}

func (x *RpcPokeChangeNatureResponse) GetPokeId() int64 {
	if x != nil {
		return x.PokeId
	}
	return 0
}

func (x *RpcPokeChangeNatureResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RpcPokeChangeNatureResponse) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

type PokeAllCateLevelInfo struct {
	state            protoimpl.MessageState        `protogen:"open.v1"`
	PokeToLevel      map[string]*PokeCateLevelInfo `protobuf:"bytes,1,rep,name=pokeToLevel,proto3" json:"pokeToLevel,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	TeamToPokeLevels map[int32]*PokeCateLevels     `protobuf:"bytes,2,rep,name=teamToPokeLevels,proto3" json:"teamToPokeLevels,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` //team 对应的 cate levels
	OtherPokeLevels  map[int32]*PokeNameIdList     `protobuf:"bytes,3,rep,name=otherPokeLevels,proto3" json:"otherPokeLevels,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`   //不区分team的poke类别
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PokeAllCateLevelInfo) Reset() {
	*x = PokeAllCateLevelInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeAllCateLevelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeAllCateLevelInfo) ProtoMessage() {}

func (x *PokeAllCateLevelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeAllCateLevelInfo.ProtoReflect.Descriptor instead.
func (*PokeAllCateLevelInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{25}
}

func (x *PokeAllCateLevelInfo) GetPokeToLevel() map[string]*PokeCateLevelInfo {
	if x != nil {
		return x.PokeToLevel
	}
	return nil
}

func (x *PokeAllCateLevelInfo) GetTeamToPokeLevels() map[int32]*PokeCateLevels {
	if x != nil {
		return x.TeamToPokeLevels
	}
	return nil
}

func (x *PokeAllCateLevelInfo) GetOtherPokeLevels() map[int32]*PokeNameIdList {
	if x != nil {
		return x.OtherPokeLevels
	}
	return nil
}

type PokeCateLevels struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	PokeLevels    map[int32]*PokeNameIdList `protobuf:"bytes,1,rep,name=pokeLevels,proto3" json:"pokeLevels,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` //level 对应 poke list
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeCateLevels) Reset() {
	*x = PokeCateLevels{}
	mi := &file_MainServer_Poke_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeCateLevels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeCateLevels) ProtoMessage() {}

func (x *PokeCateLevels) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeCateLevels.ProtoReflect.Descriptor instead.
func (*PokeCateLevels) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{26}
}

func (x *PokeCateLevels) GetPokeLevels() map[int32]*PokeNameIdList {
	if x != nil {
		return x.PokeLevels
	}
	return nil
}

type PokeNameIdList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pokes         []string               `protobuf:"bytes,1,rep,name=pokes,proto3" json:"pokes,omitempty"`
	PokeMaps      map[string]string      `protobuf:"bytes,2,rep,name=pokeMaps,proto3" json:"pokeMaps,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` //为了方便golang查找，改成字典类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeNameIdList) Reset() {
	*x = PokeNameIdList{}
	mi := &file_MainServer_Poke_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeNameIdList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeNameIdList) ProtoMessage() {}

func (x *PokeNameIdList) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeNameIdList.ProtoReflect.Descriptor instead.
func (*PokeNameIdList) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{27}
}

func (x *PokeNameIdList) GetPokes() []string {
	if x != nil {
		return x.Pokes
	}
	return nil
}

func (x *PokeNameIdList) GetPokeMaps() map[string]string {
	if x != nil {
		return x.PokeMaps
	}
	return nil
}

type PokeCateLevelInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Level         PokeCateLevel          `protobuf:"varint,1,opt,name=level,proto3,enum=MainServer.PokeCateLevel" json:"level,omitempty"`
	Chance        float32                `protobuf:"fixed32,2,opt,name=chance,proto3" json:"chance,omitempty"`
	CaptureRate   int32                  `protobuf:"varint,3,opt,name=captureRate,proto3" json:"captureRate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PokeCateLevelInfo) Reset() {
	*x = PokeCateLevelInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PokeCateLevelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PokeCateLevelInfo) ProtoMessage() {}

func (x *PokeCateLevelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PokeCateLevelInfo.ProtoReflect.Descriptor instead.
func (*PokeCateLevelInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{28}
}

func (x *PokeCateLevelInfo) GetLevel() PokeCateLevel {
	if x != nil {
		return x.Level
	}
	return PokeCateLevel_PokeCateLevel_Unknown
}

func (x *PokeCateLevelInfo) GetChance() float32 {
	if x != nil {
		return x.Chance
	}
	return 0
}

func (x *PokeCateLevelInfo) GetCaptureRate() int32 {
	if x != nil {
		return x.CaptureRate
	}
	return 0
}

type CreatePokeInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Name            string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Level           int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	Ivs             *PokeStat              `protobuf:"bytes,3,opt,name=ivs,proto3" json:"ivs,omitempty"`
	Evs             *PokeStat              `protobuf:"bytes,4,opt,name=evs,proto3" json:"evs,omitempty"`
	IvsMinV         int32                  `protobuf:"varint,5,opt,name=ivs_min_v,json=ivsMinV,proto3" json:"ivs_min_v,omitempty"`                                                                             //最少几v
	IvsMaxV         int32                  `protobuf:"varint,6,opt,name=ivs_max_v,json=ivsMaxV,proto3" json:"ivs_max_v,omitempty"`                                                                             //最多几v
	IvsVMap         map[int32]float32      `protobuf:"bytes,7,rep,name=ivs_v_map,json=ivsVMap,proto3" json:"ivs_v_map,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"fixed32,2,opt,name=value"` //几v出现的概率，比如1:0.1，那么1v的概率就是10%
	HideAbilityRate float32                `protobuf:"fixed32,8,opt,name=hide_ability_rate,json=hideAbilityRate,proto3" json:"hide_ability_rate,omitempty"`                                                    //隐藏特性概率
	MaleUpRate      float32                `protobuf:"fixed32,9,opt,name=male_up_rate,json=maleUpRate,proto3" json:"male_up_rate,omitempty"`                                                                   //雄性概率上升概率
	FemaleUpRate    float32                `protobuf:"fixed32,10,opt,name=female_up_rate,json=femaleUpRate,proto3" json:"female_up_rate,omitempty"`                                                            //雌性概率上升概率
	ShineUpRate     float32                `protobuf:"fixed32,11,opt,name=shine_up_rate,json=shineUpRate,proto3" json:"shine_up_rate,omitempty"`                                                               //闪光概率上升概率
	Ability         string                 `protobuf:"bytes,12,opt,name=ability,proto3" json:"ability,omitempty"`                                                                                              //指定特性
	IsAppointNature bool                   `protobuf:"varint,13,opt,name=is_appoint_nature,json=isAppointNature,proto3" json:"is_appoint_nature,omitempty"`                                                    //是否指定性格
	Nature          Nature                 `protobuf:"varint,14,opt,name=nature,proto3,enum=MainServer.Nature" json:"nature,omitempty"`                                                                        //指定性格
	Gender          Gender                 `protobuf:"varint,15,opt,name=gender,proto3,enum=MainServer.Gender" json:"gender,omitempty"`                                                                        //指定性别
	Item            string                 `protobuf:"bytes,16,opt,name=item,proto3" json:"item,omitempty"`                                                                                                    //携带道具
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreatePokeInfo) Reset() {
	*x = CreatePokeInfo{}
	mi := &file_MainServer_Poke_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePokeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePokeInfo) ProtoMessage() {}

func (x *CreatePokeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_MainServer_Poke_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePokeInfo.ProtoReflect.Descriptor instead.
func (*CreatePokeInfo) Descriptor() ([]byte, []int) {
	return file_MainServer_Poke_proto_rawDescGZIP(), []int{29}
}

func (x *CreatePokeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePokeInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *CreatePokeInfo) GetIvs() *PokeStat {
	if x != nil {
		return x.Ivs
	}
	return nil
}

func (x *CreatePokeInfo) GetEvs() *PokeStat {
	if x != nil {
		return x.Evs
	}
	return nil
}

func (x *CreatePokeInfo) GetIvsMinV() int32 {
	if x != nil {
		return x.IvsMinV
	}
	return 0
}

func (x *CreatePokeInfo) GetIvsMaxV() int32 {
	if x != nil {
		return x.IvsMaxV
	}
	return 0
}

func (x *CreatePokeInfo) GetIvsVMap() map[int32]float32 {
	if x != nil {
		return x.IvsVMap
	}
	return nil
}

func (x *CreatePokeInfo) GetHideAbilityRate() float32 {
	if x != nil {
		return x.HideAbilityRate
	}
	return 0
}

func (x *CreatePokeInfo) GetMaleUpRate() float32 {
	if x != nil {
		return x.MaleUpRate
	}
	return 0
}

func (x *CreatePokeInfo) GetFemaleUpRate() float32 {
	if x != nil {
		return x.FemaleUpRate
	}
	return 0
}

func (x *CreatePokeInfo) GetShineUpRate() float32 {
	if x != nil {
		return x.ShineUpRate
	}
	return 0
}

func (x *CreatePokeInfo) GetAbility() string {
	if x != nil {
		return x.Ability
	}
	return ""
}

func (x *CreatePokeInfo) GetIsAppointNature() bool {
	if x != nil {
		return x.IsAppointNature
	}
	return false
}

func (x *CreatePokeInfo) GetNature() Nature {
	if x != nil {
		return x.Nature
	}
	return Nature_NATURE_UNSPECIFIED
}

func (x *CreatePokeInfo) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_GenderNull
}

func (x *CreatePokeInfo) GetItem() string {
	if x != nil {
		return x.Item
	}
	return ""
}

var File_MainServer_Poke_proto protoreflect.FileDescriptor

const file_MainServer_Poke_proto_rawDesc = "" +
	"\n" +
	"\x15MainServer/Poke.proto\x12\n" +
	"MainServer\x1a\x1cMainServer/Transaction.proto\x1a\x1aMainServer/Inventory.proto\x1a\x1cMainServer/TrainerTeam.proto\x1a\x18MainServer/PokeExp.proto\"\xd7\b\n" +
	"\x04Poke\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1b\n" +
	"\tnick_name\x18\x04 \x01(\tR\bnickName\x12\x1b\n" +
	"\tball_name\x18\x05 \x01(\tR\bballName\x125\n" +
	"\titem_info\x18\x06 \x01(\v2\x18.MainServer.PokeItemInfoR\bitemInfo\x12\x18\n" +
	"\aability\x18\a \x01(\tR\aability\x12&\n" +
	"\x03evs\x18\b \x01(\v2\x14.MainServer.PokeStatR\x03evs\x12&\n" +
	"\x03ivs\x18\t \x01(\v2\x14.MainServer.PokeStatR\x03ivs\x12F\n" +
	"\x10transaction_type\x18\n" +
	" \x01(\x0e2\x1b.MainServer.TransactionTypeR\x0ftransactionType\x121\n" +
	"\tsale_info\x18\v \x01(\v2\x14.MainServer.SaleInfoR\bsaleInfo\x12;\n" +
	"\vborrow_info\x18\f \x01(\v2\x1a.MainServer.PokeBorrowInfoR\n" +
	"borrowInfo\x120\n" +
	"\x05moves\x18\r \x03(\v2\x1a.MainServer.PokeSimpleMoveR\x05moves\x12\x14\n" +
	"\x05level\x18\x0e \x01(\x05R\x05level\x12*\n" +
	"\x06nature\x18\x0f \x01(\x0e2\x12.MainServer.NatureR\x06nature\x122\n" +
	"\x06status\x18\x10 \x01(\v2\x1a.MainServer.PokeStatusInfoR\x06status\x12\x1e\n" +
	"\n" +
	"experience\x18\x11 \x01(\x03R\n" +
	"experience\x12(\n" +
	"\x04born\x18\x12 \x01(\v2\x14.MainServer.BornInfoR\x04born\x12\x10\n" +
	"\x03egg\x18\x13 \x01(\bR\x03egg\x12\x14\n" +
	"\x05shiny\x18\x14 \x01(\x05R\x05shiny\x12*\n" +
	"\x06gender\x18\x15 \x01(\x0e2\x12.MainServer.GenderR\x06gender\x12\x15\n" +
	"\x06hp_sub\x18\x16 \x01(\x05R\x05hpSub\x12\x1c\n" +
	"\thappiness\x18\x17 \x01(\x05R\thappiness\x125\n" +
	"\tsys_extra\x18\x18 \x01(\v2\x18.MainServer.PokeSysExtraR\bsysExtra\x12+\n" +
	"\x05extra\x18\x19 \x01(\v2\x15.MainServer.PokeExtraR\x05extra\x12\x18\n" +
	"\arelease\x18\x1a \x01(\bR\arelease\x123\n" +
	"\thonorInfo\x18\x1b \x01(\v2\x15.MainServer.HonorInfoR\thonorInfo\x12\x1e\n" +
	"\n" +
	"breedCount\x18\x1c \x01(\x05R\n" +
	"breedCount\x12\x1b\n" +
	"\tcreate_ts\x18\x1d \x01(\x03R\bcreateTs\x12\x1b\n" +
	"\tupdate_ts\x18\x1e \x01(\x03R\bupdateTs\"\xf2\x01\n" +
	"\fPokeItemInfo\x12!\n" +
	"\finventory_id\x18\x01 \x01(\x03R\vinventoryId\x12\x10\n" +
	"\x03tid\x18\x02 \x01(\x03R\x03tid\x12\x1b\n" +
	"\titem_name\x18\x03 \x01(\tR\bitemName\x12\x1a\n" +
	"\bquantity\x18\x04 \x01(\x05R\bquantity\x12>\n" +
	"\x0eitem_sale_type\x18\x05 \x01(\x0e2\x18.MainServer.ItemSaleTypeR\fitemSaleType\x124\n" +
	"\tteam_type\x18\x06 \x01(\x0e2\x17.MainServer.TrainerTeamR\bteamType\"~\n" +
	"\x0ePokeStatusInfo\x12;\n" +
	"\vlive_status\x18\x01 \x01(\x0e2\x1a.MainServer.PokeLiveStatusR\n" +
	"liveStatus\x12\x17\n" +
	"\ais_only\x18\x02 \x01(\bR\x06isOnly\x12\x16\n" +
	"\x06sealed\x18\x03 \x01(\tR\x06sealed\"\xd0\x01\n" +
	"\x0ePokeBorrowInfo\x12\x1b\n" +
	"\tborrow_ts\x18\x01 \x01(\x03R\bborrowTs\x12\x1b\n" +
	"\treturn_ts\x18\x02 \x01(\x03R\breturnTs\x12\x1f\n" +
	"\vcan_renewed\x18\x03 \x01(\bR\n" +
	"canRenewed\x12*\n" +
	"\x11borrow_trainer_id\x18\x04 \x01(\x03R\x0fborrowTrainerId\x12!\n" +
	"\fdeposit_coin\x18\x05 \x01(\x03R\vdepositCoin\x12\x14\n" +
	"\x05price\x18\x06 \x01(\x05R\x05price\"#\n" +
	"\tHonorInfo\x12\x16\n" +
	"\x06badges\x18\x01 \x03(\tR\x06badges\"\xb8\x01\n" +
	"\bSaleInfo\x12\x14\n" +
	"\x05price\x18\x01 \x01(\x05R\x05price\x12!\n" +
	"\fspecial_coin\x18\x02 \x01(\x05R\vspecialCoin\x12\x1b\n" +
	"\tcreate_ts\x18\x03 \x01(\x03R\bcreateTs\x12\x19\n" +
	"\brent_day\x18\x04 \x01(\x05R\arentDay\x12\x18\n" +
	"\arenewed\x18\x05 \x01(\bR\arenewed\x12!\n" +
	"\fdeposit_coin\x18\x06 \x01(\x05R\vdepositCoin\"t\n" +
	"\bPokeStat\x12\x0e\n" +
	"\x02hp\x18\x01 \x01(\x05R\x02hp\x12\x10\n" +
	"\x03atk\x18\x02 \x01(\x05R\x03atk\x12\x10\n" +
	"\x03def\x18\x03 \x01(\x05R\x03def\x12\x10\n" +
	"\x03spa\x18\x04 \x01(\x05R\x03spa\x12\x10\n" +
	"\x03spd\x18\x05 \x01(\x05R\x03spd\x12\x10\n" +
	"\x03spe\x18\x06 \x01(\x05R\x03spe\"\xeb\x01\n" +
	"\rPokeBoostStat\x12\x0e\n" +
	"\x02hp\x18\x01 \x01(\x05R\x02hp\x12\x16\n" +
	"\x06attack\x18\x02 \x01(\x05R\x06attack\x12\x18\n" +
	"\adefense\x18\x03 \x01(\x05R\adefense\x12$\n" +
	"\rspecialAttack\x18\x04 \x01(\x05R\rspecialAttack\x12&\n" +
	"\x0especialDefense\x18\x05 \x01(\x05R\x0especialDefense\x12\x14\n" +
	"\x05speed\x18\x06 \x01(\x05R\x05speed\x12\x1a\n" +
	"\baccuracy\x18\a \x01(\x05R\baccuracy\x12\x18\n" +
	"\aevasion\x18\b \x01(\x05R\aevasion\"R\n" +
	"\x0ePokeSimpleMove\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x15\n" +
	"\x06pp_pro\x18\x02 \x01(\x05R\x05ppPro\x12\x15\n" +
	"\x06pp_sub\x18\x03 \x01(\x05R\x05ppSub\"\xda\x01\n" +
	"\bBornInfo\x125\n" +
	"\tborn_type\x18\x01 \x01(\x0e2\x18.MainServer.BornInfoTypeR\bbornType\x12\x17\n" +
	"\aborn_ts\x18\x02 \x01(\x03R\x06bornTs\x12\x10\n" +
	"\x03tid\x18\x03 \x01(\x03R\x03tid\x12!\n" +
	"\ftrainer_name\x18\x04 \x01(\tR\vtrainerName\x12,\n" +
	"\x12frist_trainer_name\x18\x05 \x01(\tR\x10fristTrainerName\x12\x1b\n" +
	"\tis_battle\x18\x06 \x01(\bR\bisBattle\"n\n" +
	"\fPokeSysExtra\x12\"\n" +
	"\fdynamaxLevel\x18\x01 \x01(\x05R\fdynamaxLevel\x12\x1e\n" +
	"\n" +
	"gigantamax\x18\x02 \x01(\bR\n" +
	"gigantamax\x12\x1a\n" +
	"\bterastal\x18\x03 \x01(\tR\bterastal\"\x92\x01\n" +
	"\tPokeExtra\x12\x1a\n" +
	"\btempItem\x18\x01 \x01(\tR\btempItem\x12.\n" +
	"\atempEvs\x18\x02 \x01(\v2\x14.MainServer.PokeStatR\atempEvs\x129\n" +
	"\vlastExpInfo\x18\x03 \x01(\v2\x17.MainServer.PokeExpInfoR\vlastExpInfo\"~\n" +
	"\x12LastExpInfoRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x129\n" +
	"\vlastExpInfo\x18\x02 \x01(\v2\x17.MainServer.PokeExpInfoR\vlastExpInfo\x12\x14\n" +
	"\x05moves\x18\x03 \x03(\tR\x05moves\"\x16\n" +
	"\x14RpcPokeUpdateRequest\"\x17\n" +
	"\x15RpcPokeUpdateResponse\"Z\n" +
	"\x17RpcPokeChangeEvsRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12&\n" +
	"\x03evs\x18\x02 \x01(\v2\x14.MainServer.PokeStatR\x03evs\"[\n" +
	"\x18RpcPokeChangeEvsResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12&\n" +
	"\x03evs\x18\x02 \x01(\v2\x14.MainServer.PokeStatR\x03evs\"m\n" +
	"\x1bRpcPokeChangeAbilityRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\aability\x18\x02 \x01(\tR\aability\x12\x1b\n" +
	"\tuse_count\x18\x03 \x01(\x05R\buseCount\"Q\n" +
	"\x1cRpcPokeChangeAbilityResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\"\x8c\x01\n" +
	"\x18RpcPokeChangeMoveRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x1b\n" +
	"\tmove_name\x18\x02 \x01(\tR\bmoveName\x12\x1d\n" +
	"\n" +
	"move_index\x18\x03 \x01(\x05R\tmoveIndex\x12\x1b\n" +
	"\tis_remove\x18\x04 \x01(\bR\bisRemove\"\x80\x01\n" +
	"\x19RpcPokeChangeMoveResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x120\n" +
	"\x05moves\x18\x03 \x03(\v2\x1a.MainServer.PokeSimpleMoveR\x05moves\"\x9e\x01\n" +
	"\x14RpcPokeUseCapRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x19\n" +
	"\bcap_name\x18\x02 \x01(\tR\acapName\x125\n" +
	"\bstat_key\x18\x03 \x01(\x0e2\x1a.MainServer.PokemonStatKeyR\astatKey\x12\x1b\n" +
	"\tuse_count\x18\x04 \x01(\x05R\buseCount\"\x86\x01\n" +
	"\x15RpcPokeUseCapResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x1d\n" +
	"\n" +
	"use_result\x18\x02 \x01(\x05R\tuseResult\x125\n" +
	"\bstat_key\x18\x03 \x01(\x0e2\x1a.MainServer.PokemonStatKeyR\astatKey\"~\n" +
	"\x1aRpcPokeChangeNatureRequest\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12*\n" +
	"\x06nature\x18\x02 \x01(\x0e2\x12.MainServer.NatureR\x06nature\x12\x1b\n" +
	"\tuse_count\x18\x03 \x01(\x05R\buseCount\"|\n" +
	"\x1bRpcPokeChangeNatureResponse\x12\x17\n" +
	"\apoke_id\x18\x01 \x01(\x03R\x06pokeId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12*\n" +
	"\x06nature\x18\x03 \x01(\x0e2\x12.MainServer.NatureR\x06nature\"\xd0\x04\n" +
	"\x14PokeAllCateLevelInfo\x12S\n" +
	"\vpokeToLevel\x18\x01 \x03(\v21.MainServer.PokeAllCateLevelInfo.PokeToLevelEntryR\vpokeToLevel\x12b\n" +
	"\x10teamToPokeLevels\x18\x02 \x03(\v26.MainServer.PokeAllCateLevelInfo.TeamToPokeLevelsEntryR\x10teamToPokeLevels\x12_\n" +
	"\x0fotherPokeLevels\x18\x03 \x03(\v25.MainServer.PokeAllCateLevelInfo.OtherPokeLevelsEntryR\x0fotherPokeLevels\x1a]\n" +
	"\x10PokeToLevelEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.MainServer.PokeCateLevelInfoR\x05value:\x028\x01\x1a_\n" +
	"\x15TeamToPokeLevelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x120\n" +
	"\x05value\x18\x02 \x01(\v2\x1a.MainServer.PokeCateLevelsR\x05value:\x028\x01\x1a^\n" +
	"\x14OtherPokeLevelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x120\n" +
	"\x05value\x18\x02 \x01(\v2\x1a.MainServer.PokeNameIdListR\x05value:\x028\x01\"\xb7\x01\n" +
	"\x0ePokeCateLevels\x12J\n" +
	"\n" +
	"pokeLevels\x18\x01 \x03(\v2*.MainServer.PokeCateLevels.PokeLevelsEntryR\n" +
	"pokeLevels\x1aY\n" +
	"\x0fPokeLevelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x120\n" +
	"\x05value\x18\x02 \x01(\v2\x1a.MainServer.PokeNameIdListR\x05value:\x028\x01\"\xa9\x01\n" +
	"\x0ePokeNameIdList\x12\x14\n" +
	"\x05pokes\x18\x01 \x03(\tR\x05pokes\x12D\n" +
	"\bpokeMaps\x18\x02 \x03(\v2(.MainServer.PokeNameIdList.PokeMapsEntryR\bpokeMaps\x1a;\n" +
	"\rPokeMapsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"~\n" +
	"\x11PokeCateLevelInfo\x12/\n" +
	"\x05level\x18\x01 \x01(\x0e2\x19.MainServer.PokeCateLevelR\x05level\x12\x16\n" +
	"\x06chance\x18\x02 \x01(\x02R\x06chance\x12 \n" +
	"\vcaptureRate\x18\x03 \x01(\x05R\vcaptureRate\"\x8d\x05\n" +
	"\x0eCreatePokeInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05level\x18\x02 \x01(\x05R\x05level\x12&\n" +
	"\x03ivs\x18\x03 \x01(\v2\x14.MainServer.PokeStatR\x03ivs\x12&\n" +
	"\x03evs\x18\x04 \x01(\v2\x14.MainServer.PokeStatR\x03evs\x12\x1a\n" +
	"\tivs_min_v\x18\x05 \x01(\x05R\aivsMinV\x12\x1a\n" +
	"\tivs_max_v\x18\x06 \x01(\x05R\aivsMaxV\x12C\n" +
	"\tivs_v_map\x18\a \x03(\v2'.MainServer.CreatePokeInfo.IvsVMapEntryR\aivsVMap\x12*\n" +
	"\x11hide_ability_rate\x18\b \x01(\x02R\x0fhideAbilityRate\x12 \n" +
	"\fmale_up_rate\x18\t \x01(\x02R\n" +
	"maleUpRate\x12$\n" +
	"\x0efemale_up_rate\x18\n" +
	" \x01(\x02R\ffemaleUpRate\x12\"\n" +
	"\rshine_up_rate\x18\v \x01(\x02R\vshineUpRate\x12\x18\n" +
	"\aability\x18\f \x01(\tR\aability\x12*\n" +
	"\x11is_appoint_nature\x18\r \x01(\bR\x0fisAppointNature\x12*\n" +
	"\x06nature\x18\x0e \x01(\x0e2\x12.MainServer.NatureR\x06nature\x12*\n" +
	"\x06gender\x18\x0f \x01(\x0e2\x12.MainServer.GenderR\x06gender\x12\x12\n" +
	"\x04item\x18\x10 \x01(\tR\x04item\x1a:\n" +
	"\fIvsVMapEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x02R\x05value:\x028\x01*\x8b\x02\n" +
	"\x0ePokeLiveStatus\x12\x19\n" +
	"\x15PokeLiveStatus_Normal\x10\x00\x12\x1a\n" +
	"\x16PokeLiveStatus_Fainted\x10\x01\x12\x19\n" +
	"\x15PokeLiveStatus_Frozen\x10\x02\x12\x1b\n" +
	"\x17PokeLiveStatus_Poisoned\x10\x03\x12\x19\n" +
	"\x15PokeLiveStatus_Burned\x10\x04\x12\x1c\n" +
	"\x18PokeLiveStatus_Paralyzed\x10\x05\x12\x1b\n" +
	"\x17PokeLiveStatus_Sleeping\x10\x06\x12\x1b\n" +
	"\x17PokeLiveStatus_Confused\x10\a\x12\x17\n" +
	"\x13PokeLiveStatus_Dead\x10\b*k\n" +
	"\fBornInfoType\x12\x15\n" +
	"\x11BornInfoType_Wild\x10\x00\x12\x14\n" +
	"\x10BornInfoType_Egg\x10\x01\x12\x17\n" +
	"\x13BornInfoType_Summon\x10\x02\x12\x15\n" +
	"\x11BornInfoType_Gift\x10\x03*\xa8\x03\n" +
	"\fPokeTypeEnum\x12\x14\n" +
	"\x10PokeType_Unknown\x10\x00\x12\x13\n" +
	"\x0fPokeType_Normal\x10\x01\x12\x11\n" +
	"\rPokeType_Fire\x10\x02\x12\x12\n" +
	"\x0ePokeType_Water\x10\x03\x12\x15\n" +
	"\x11PokeType_Electric\x10\x04\x12\x12\n" +
	"\x0ePokeType_Grass\x10\x05\x12\x10\n" +
	"\fPokeType_Ice\x10\x06\x12\x15\n" +
	"\x11PokeType_Fighting\x10\a\x12\x13\n" +
	"\x0fPokeType_Poison\x10\b\x12\x13\n" +
	"\x0fPokeType_Ground\x10\t\x12\x13\n" +
	"\x0fPokeType_Flying\x10\n" +
	"\x12\x14\n" +
	"\x10PokeType_Psychic\x10\v\x12\x10\n" +
	"\fPokeType_Bug\x10\f\x12\x11\n" +
	"\rPokeType_Rock\x10\r\x12\x12\n" +
	"\x0ePokeType_Ghost\x10\x0e\x12\x13\n" +
	"\x0fPokeType_Dragon\x10\x0f\x12\x11\n" +
	"\rPokeType_Dark\x10\x10\x12\x12\n" +
	"\x0ePokeType_Steel\x10\x11\x12\x12\n" +
	"\x0ePokeType_Fairy\x10\x12\x12\x14\n" +
	"\x10PokeType_Stellar\x10\x13*\xbf\x02\n" +
	"\x06Nature\x12\x16\n" +
	"\x12NATURE_UNSPECIFIED\x10\x00\x12\v\n" +
	"\aADAMANT\x10\x01\x12\v\n" +
	"\aBASHFUL\x10\x02\x12\b\n" +
	"\x04BOLD\x10\x03\x12\t\n" +
	"\x05BRAVE\x10\x04\x12\b\n" +
	"\x04CALM\x10\x05\x12\v\n" +
	"\aCAREFUL\x10\x06\x12\n" +
	"\n" +
	"\x06DOCILE\x10\a\x12\n" +
	"\n" +
	"\x06GENTLE\x10\b\x12\t\n" +
	"\x05HARDY\x10\t\x12\t\n" +
	"\x05HASTY\x10\n" +
	"\x12\n" +
	"\n" +
	"\x06IMPISH\x10\v\x12\t\n" +
	"\x05JOLLY\x10\f\x12\a\n" +
	"\x03LAX\x10\r\x12\n" +
	"\n" +
	"\x06LONELY\x10\x0e\x12\b\n" +
	"\x04MILD\x10\x0f\x12\n" +
	"\n" +
	"\x06MODEST\x10\x10\x12\t\n" +
	"\x05NAIVE\x10\x11\x12\v\n" +
	"\aNAUGHTY\x10\x12\x12\t\n" +
	"\x05QUIET\x10\x13\x12\n" +
	"\n" +
	"\x06QUIRKY\x10\x14\x12\b\n" +
	"\x04RASH\x10\x15\x12\v\n" +
	"\aRELAXED\x10\x16\x12\t\n" +
	"\x05SASSY\x10\x17\x12\v\n" +
	"\aSERIOUS\x10\x18\x12\t\n" +
	"\x05TIMID\x10\x19*-\n" +
	"\x06Gender\x12\x0e\n" +
	"\n" +
	"GenderNull\x10\x00\x12\x05\n" +
	"\x01M\x10\x01\x12\x05\n" +
	"\x01F\x10\x02\x12\x05\n" +
	"\x01N\x10\x03*\xd7\x01\n" +
	"\x0ePokemonStatKey\x12\x17\n" +
	"\x13StatKey_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"StatKey_HP\x10\x01\x12\x12\n" +
	"\x0eStatKey_Attack\x10\x02\x12\x13\n" +
	"\x0fStatKey_Defense\x10\x03\x12\x19\n" +
	"\x15StatKey_SpecialAttack\x10\x04\x12\x1a\n" +
	"\x16StatKey_SpecialDefense\x10\x05\x12\x11\n" +
	"\rStatKey_Speed\x10\x06\x12\x14\n" +
	"\x10StatKey_Accuracy\x10\a\x12\x13\n" +
	"\x0fStatKey_Evasion\x10\b*\xf6\x02\n" +
	"\rPokeCateLevel\x12\x19\n" +
	"\x15PokeCateLevel_Unknown\x10\x00\x12\x13\n" +
	"\x0fPokeCateLevel_1\x10\x01\x12\x13\n" +
	"\x0fPokeCateLevel_2\x10\x02\x12\x13\n" +
	"\x0fPokeCateLevel_3\x10\x03\x12\x13\n" +
	"\x0fPokeCateLevel_4\x10\x04\x12\x13\n" +
	"\x0fPokeCateLevel_5\x10\x05\x12\x17\n" +
	"\x13PokeCateLevel_First\x10\x06\x12\x16\n" +
	"\x12PokeCateLevel_Late\x10\a\x12\x17\n" +
	"\x13PokeCateLevel_Ultra\x10\b\x12\x19\n" +
	"\x15PokeCateLevel_Paradox\x10\t\x12!\n" +
	"\x1dPokeCateLevel_LegendaryNormal\x10\n" +
	"\x12\"\n" +
	"\x1ePokeCateLevel_LegendarySpecial\x10\v\x12\x1a\n" +
	"\x16PokeCateLevel_Mythical\x10\f\x12\x19\n" +
	"\x15PokeCateLevel_Special\x10\rB!Z\x1fgo-nakama-poke/proto/MainServerb\x06proto3"

var (
	file_MainServer_Poke_proto_rawDescOnce sync.Once
	file_MainServer_Poke_proto_rawDescData []byte
)

func file_MainServer_Poke_proto_rawDescGZIP() []byte {
	file_MainServer_Poke_proto_rawDescOnce.Do(func() {
		file_MainServer_Poke_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_MainServer_Poke_proto_rawDesc), len(file_MainServer_Poke_proto_rawDesc)))
	})
	return file_MainServer_Poke_proto_rawDescData
}

var file_MainServer_Poke_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_MainServer_Poke_proto_msgTypes = make([]protoimpl.MessageInfo, 36)
var file_MainServer_Poke_proto_goTypes = []any{
	(PokeLiveStatus)(0),                  // 0: MainServer.PokeLiveStatus
	(BornInfoType)(0),                    // 1: MainServer.BornInfoType
	(PokeTypeEnum)(0),                    // 2: MainServer.PokeTypeEnum
	(Nature)(0),                          // 3: MainServer.Nature
	(Gender)(0),                          // 4: MainServer.Gender
	(PokemonStatKey)(0),                  // 5: MainServer.PokemonStatKey
	(PokeCateLevel)(0),                   // 6: MainServer.PokeCateLevel
	(*Poke)(nil),                         // 7: MainServer.Poke
	(*PokeItemInfo)(nil),                 // 8: MainServer.PokeItemInfo
	(*PokeStatusInfo)(nil),               // 9: MainServer.PokeStatusInfo
	(*PokeBorrowInfo)(nil),               // 10: MainServer.PokeBorrowInfo
	(*HonorInfo)(nil),                    // 11: MainServer.HonorInfo
	(*SaleInfo)(nil),                     // 12: MainServer.SaleInfo
	(*PokeStat)(nil),                     // 13: MainServer.PokeStat
	(*PokeBoostStat)(nil),                // 14: MainServer.PokeBoostStat
	(*PokeSimpleMove)(nil),               // 15: MainServer.PokeSimpleMove
	(*BornInfo)(nil),                     // 16: MainServer.BornInfo
	(*PokeSysExtra)(nil),                 // 17: MainServer.PokeSysExtra
	(*PokeExtra)(nil),                    // 18: MainServer.PokeExtra
	(*LastExpInfoRequest)(nil),           // 19: MainServer.LastExpInfoRequest
	(*RpcPokeUpdateRequest)(nil),         // 20: MainServer.RpcPokeUpdateRequest
	(*RpcPokeUpdateResponse)(nil),        // 21: MainServer.RpcPokeUpdateResponse
	(*RpcPokeChangeEvsRequest)(nil),      // 22: MainServer.RpcPokeChangeEvsRequest
	(*RpcPokeChangeEvsResponse)(nil),     // 23: MainServer.RpcPokeChangeEvsResponse
	(*RpcPokeChangeAbilityRequest)(nil),  // 24: MainServer.RpcPokeChangeAbilityRequest
	(*RpcPokeChangeAbilityResponse)(nil), // 25: MainServer.RpcPokeChangeAbilityResponse
	(*RpcPokeChangeMoveRequest)(nil),     // 26: MainServer.RpcPokeChangeMoveRequest
	(*RpcPokeChangeMoveResponse)(nil),    // 27: MainServer.RpcPokeChangeMoveResponse
	(*RpcPokeUseCapRequest)(nil),         // 28: MainServer.RpcPokeUseCapRequest
	(*RpcPokeUseCapResponse)(nil),        // 29: MainServer.RpcPokeUseCapResponse
	(*RpcPokeChangeNatureRequest)(nil),   // 30: MainServer.RpcPokeChangeNatureRequest
	(*RpcPokeChangeNatureResponse)(nil),  // 31: MainServer.RpcPokeChangeNatureResponse
	(*PokeAllCateLevelInfo)(nil),         // 32: MainServer.PokeAllCateLevelInfo
	(*PokeCateLevels)(nil),               // 33: MainServer.PokeCateLevels
	(*PokeNameIdList)(nil),               // 34: MainServer.PokeNameIdList
	(*PokeCateLevelInfo)(nil),            // 35: MainServer.PokeCateLevelInfo
	(*CreatePokeInfo)(nil),               // 36: MainServer.CreatePokeInfo
	nil,                                  // 37: MainServer.PokeAllCateLevelInfo.PokeToLevelEntry
	nil,                                  // 38: MainServer.PokeAllCateLevelInfo.TeamToPokeLevelsEntry
	nil,                                  // 39: MainServer.PokeAllCateLevelInfo.OtherPokeLevelsEntry
	nil,                                  // 40: MainServer.PokeCateLevels.PokeLevelsEntry
	nil,                                  // 41: MainServer.PokeNameIdList.PokeMapsEntry
	nil,                                  // 42: MainServer.CreatePokeInfo.IvsVMapEntry
	(TransactionType)(0),                 // 43: MainServer.TransactionType
	(ItemSaleType)(0),                    // 44: MainServer.ItemSaleType
	(TrainerTeam)(0),                     // 45: MainServer.TrainerTeam
	(*PokeExpInfo)(nil),                  // 46: MainServer.PokeExpInfo
}
var file_MainServer_Poke_proto_depIdxs = []int32{
	8,  // 0: MainServer.Poke.item_info:type_name -> MainServer.PokeItemInfo
	13, // 1: MainServer.Poke.evs:type_name -> MainServer.PokeStat
	13, // 2: MainServer.Poke.ivs:type_name -> MainServer.PokeStat
	43, // 3: MainServer.Poke.transaction_type:type_name -> MainServer.TransactionType
	12, // 4: MainServer.Poke.sale_info:type_name -> MainServer.SaleInfo
	10, // 5: MainServer.Poke.borrow_info:type_name -> MainServer.PokeBorrowInfo
	15, // 6: MainServer.Poke.moves:type_name -> MainServer.PokeSimpleMove
	3,  // 7: MainServer.Poke.nature:type_name -> MainServer.Nature
	9,  // 8: MainServer.Poke.status:type_name -> MainServer.PokeStatusInfo
	16, // 9: MainServer.Poke.born:type_name -> MainServer.BornInfo
	4,  // 10: MainServer.Poke.gender:type_name -> MainServer.Gender
	17, // 11: MainServer.Poke.sys_extra:type_name -> MainServer.PokeSysExtra
	18, // 12: MainServer.Poke.extra:type_name -> MainServer.PokeExtra
	11, // 13: MainServer.Poke.honorInfo:type_name -> MainServer.HonorInfo
	44, // 14: MainServer.PokeItemInfo.item_sale_type:type_name -> MainServer.ItemSaleType
	45, // 15: MainServer.PokeItemInfo.team_type:type_name -> MainServer.TrainerTeam
	0,  // 16: MainServer.PokeStatusInfo.live_status:type_name -> MainServer.PokeLiveStatus
	1,  // 17: MainServer.BornInfo.born_type:type_name -> MainServer.BornInfoType
	13, // 18: MainServer.PokeExtra.tempEvs:type_name -> MainServer.PokeStat
	46, // 19: MainServer.PokeExtra.lastExpInfo:type_name -> MainServer.PokeExpInfo
	46, // 20: MainServer.LastExpInfoRequest.lastExpInfo:type_name -> MainServer.PokeExpInfo
	13, // 21: MainServer.RpcPokeChangeEvsRequest.evs:type_name -> MainServer.PokeStat
	13, // 22: MainServer.RpcPokeChangeEvsResponse.evs:type_name -> MainServer.PokeStat
	15, // 23: MainServer.RpcPokeChangeMoveResponse.moves:type_name -> MainServer.PokeSimpleMove
	5,  // 24: MainServer.RpcPokeUseCapRequest.stat_key:type_name -> MainServer.PokemonStatKey
	5,  // 25: MainServer.RpcPokeUseCapResponse.stat_key:type_name -> MainServer.PokemonStatKey
	3,  // 26: MainServer.RpcPokeChangeNatureRequest.nature:type_name -> MainServer.Nature
	3,  // 27: MainServer.RpcPokeChangeNatureResponse.nature:type_name -> MainServer.Nature
	37, // 28: MainServer.PokeAllCateLevelInfo.pokeToLevel:type_name -> MainServer.PokeAllCateLevelInfo.PokeToLevelEntry
	38, // 29: MainServer.PokeAllCateLevelInfo.teamToPokeLevels:type_name -> MainServer.PokeAllCateLevelInfo.TeamToPokeLevelsEntry
	39, // 30: MainServer.PokeAllCateLevelInfo.otherPokeLevels:type_name -> MainServer.PokeAllCateLevelInfo.OtherPokeLevelsEntry
	40, // 31: MainServer.PokeCateLevels.pokeLevels:type_name -> MainServer.PokeCateLevels.PokeLevelsEntry
	41, // 32: MainServer.PokeNameIdList.pokeMaps:type_name -> MainServer.PokeNameIdList.PokeMapsEntry
	6,  // 33: MainServer.PokeCateLevelInfo.level:type_name -> MainServer.PokeCateLevel
	13, // 34: MainServer.CreatePokeInfo.ivs:type_name -> MainServer.PokeStat
	13, // 35: MainServer.CreatePokeInfo.evs:type_name -> MainServer.PokeStat
	42, // 36: MainServer.CreatePokeInfo.ivs_v_map:type_name -> MainServer.CreatePokeInfo.IvsVMapEntry
	3,  // 37: MainServer.CreatePokeInfo.nature:type_name -> MainServer.Nature
	4,  // 38: MainServer.CreatePokeInfo.gender:type_name -> MainServer.Gender
	35, // 39: MainServer.PokeAllCateLevelInfo.PokeToLevelEntry.value:type_name -> MainServer.PokeCateLevelInfo
	33, // 40: MainServer.PokeAllCateLevelInfo.TeamToPokeLevelsEntry.value:type_name -> MainServer.PokeCateLevels
	34, // 41: MainServer.PokeAllCateLevelInfo.OtherPokeLevelsEntry.value:type_name -> MainServer.PokeNameIdList
	34, // 42: MainServer.PokeCateLevels.PokeLevelsEntry.value:type_name -> MainServer.PokeNameIdList
	43, // [43:43] is the sub-list for method output_type
	43, // [43:43] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_MainServer_Poke_proto_init() }
func file_MainServer_Poke_proto_init() {
	if File_MainServer_Poke_proto != nil {
		return
	}
	file_MainServer_Transaction_proto_init()
	file_MainServer_Inventory_proto_init()
	file_MainServer_TrainerTeam_proto_init()
	file_MainServer_PokeExp_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_MainServer_Poke_proto_rawDesc), len(file_MainServer_Poke_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   36,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_MainServer_Poke_proto_goTypes,
		DependencyIndexes: file_MainServer_Poke_proto_depIdxs,
		EnumInfos:         file_MainServer_Poke_proto_enumTypes,
		MessageInfos:      file_MainServer_Poke_proto_msgTypes,
	}.Build()
	File_MainServer_Poke_proto = out.File
	file_MainServer_Poke_proto_goTypes = nil
	file_MainServer_Poke_proto_depIdxs = nil
}
