package trainer

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/nconst"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/tool"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const TableTrainerName = "trainer"

func initTrainers(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createTrainerTableIfNotExists(ctx, logger, db)
}

// 初始宝可梦名称与对应世代的字典
var starters = map[string]int{
	// 第一代
	"bulbasaur": 1, "charmander": 1, "squirtle": 1,
	// 第二代
	"chikorita": 2, "cyndaquil": 2, "totodile": 2,
	// 第三代
	"treecko": 3, "torchic": 3, "mudkip": 3,
	// 第四代
	"turtwig": 4, "chimchar": 4, "piplup": 4,
	// 第五代
	"snivy": 5, "tepig": 5, "oshawott": 5,
	// 第六代
	"chespin": 6, "fennekin": 6, "froakie": 6,
	// 第七代
	"rowlet": 7, "litten": 7, "popplio": 7,
	// 第八代
	"grookey": 8, "scorbunny": 8, "sobble": 8,
	// 第九代
	"sprigatito": 9, "fuecoco": 9, "quaxly": 9,
}
var initCloth = map[string]int{
	// 第一代
	nconst.Cloth_Id_Init_01: 1, nconst.Cloth_Id_Init_02: 1, nconst.Cloth_Id_Init_03: 1,
}

func createTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, trainer *MainServer.Trainer) (string, error) {

	trainer.CreateTs = time.Now().UnixMilli()
	if trainer.Cloth.Type != MainServer.TrainerClothType_CLOTH_TYPE_OLD_NIN || initCloth[trainer.Cloth.Name] == 0 {
		return "", fmt.Errorf("cloth name err")
	}
	// 初始化 StrictInfo 字段
	// if trainer.StrictInfo == nil {
	// }
	trainer.StrictInfo = &MainServer.TrainerStrictInfo{}
	// 初始化 ActionInfo 字段
	// if trainer.ActionInfo == nil {
	// }
	trainer.ActionInfo = &MainServer.TrainerActionInfo{}
	trainer.Items = map[string]*MainServer.TrainerItemInfo{}
	trainer.Badges = &MainServer.TrainerBadges{}
	trainer.BoxStatus = &MainServer.TrainerBoxStatus{}
	trainer.Decoration = &MainServer.TrainerDecoration{}
	trainer.SessionInfo = &MainServer.TrainerSessionInfo{}
	trainer.FollowPoke = &MainServer.TrainerFollowPoke{}
	trainer.TeamInfo = &MainServer.TrainerOnTeamInfo{}

	// trainer.Uid = trainer.Uid
	trainer.Id = -1
	trainer.ActionInfo.Action = MainServer.TrainerActionType_idle
	if len(trainer.PokeIds) != 1 {
		return "", fmt.Errorf("Trainer poke count err %d", len(trainer.PokeIds))
	}
	pokeName := trainer.PokeIds[0]
	has, gen := isStarter(pokeName)
	if !has {
		return "", fmt.Errorf("Trainer poke name err")
	}
	trainer.ActionInfo.LastMainLand = &MainServer.TrainerLastMainLandLocInfo{
		Loc: &MainServer.TrainerLoc{},
	}
	if gen == 1 {
		trainer.ActionInfo.LastMainLand.Loc.MainLandType = MainServer.MainLandType_MainLand_HeartGold
		trainer.ActionInfo.LastMainLand.LastPcName = "poke_gen1"
	} else if gen == 2 {
		trainer.ActionInfo.LastMainLand.Loc.MainLandType = MainServer.MainLandType_MainLand_HeartGold
		trainer.ActionInfo.LastMainLand.LastPcName = "poke_gen2"
	} else {
		trainer.ActionInfo.LastMainLand.Loc.MainLandType = MainServer.MainLandType_MainLand_HeartGold
		trainer.ActionInfo.LastMainLand.LastPcName = "poke_gen1"
	}
	unique, err := isNameValid(ctx, db, trainer.Name)
	if err != nil {
		return "", fmt.Errorf("failed to begin createPoke: %v", err)
	}
	if !unique {
		return "", fmt.Errorf("trainer Name err")
	}
	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("failed to begin transaction: %v", err)
	}
	createPokeInfo := &MainServer.CreatePokeInfo{
		Name:  pokeName,
		Level: 5,
	}

	poke_new, err := poke.CreatePoke(ctx, tx, createPokeInfo)
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to begin createPoke: %v", err)
	}
	idStr := strconv.FormatInt(int64(poke_new.Id), 10)
	trainer.PokeIds = []string{idStr}

	tid, err := UpsertTrainer(ctx, logger, tx, trainer)
	// 插入 Trainer 数据
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to upsert trainer: %v", err)
	}
	poke_new.Tid = trainer.Id
	err = poke.UpdatePokeData(ctx, tx, poke_new)
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to upsert trainer: %v", err)
	}
	// 初始化 Trainer 的 PokeBox
	if err := initializeTrainerBoxes(ctx, logger, tx, trainer, false); err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to initialize trainer box: %v", err)
	}
	err = AddTrainerCloth(ctx, logger, tx, trainer.Id, MainServer.TrainerClothType_CLOTH_TYPE_OLD_NIN, trainer.Cloth.Name)
	if err != nil {
		tx.Rollback()
		return "", fmt.Errorf("failed to add trainer cloth: %v", err)
	}
	// 训练师库存
	// if err := inventory.InitTrainerInventory(ctx, logger, tid, tx); err != nil {
	// 	tx.Rollback()
	// 	return "", fmt.Errorf("failed to Init TrainerInventory : %v", err)
	// }

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("failed to commit transaction: %v", err)
	}

	return strconv.FormatInt(tid, 10), nil
}
func UpdateTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, trainer *MainServer.Trainer) (*MainServer.Trainer, error) {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %v", err)
	}
	//更新时间
	UpsertTrainer(ctx, logger, tx, trainer)
	// 提交事务
	if err := tx.Commit(); err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to commit transaction: %v", err)
	}
	return trainer, nil
}

func UserHasTrainer(db *sql.DB, userId string, tid int64) bool {
	count := userTrainerCount(db, userId, tid, false)
	// 如果 count > 0，表示有匹配的记录
	return count > 0
}

// Check if a given name (with whitespace removed) is unique in the database
// isNamePass 检查 name 是否合格（去除空格后唯一且符合其他合格性规则）
func isNameValid(ctx context.Context, db *sql.DB, name string) (bool, error) {
	// 去除 name 中的所有空格
	cleanedName := strings.ReplaceAll(name, " ", "")
	// 基础检查：name 去掉空格后是否为空
	if cleanedName == "" {
		return false, fmt.Errorf("name cannot be empty after removing spaces")
	}

	// 检查长度（假设合格名称长度范围为 3 到 20 个字符）
	if len(cleanedName) < 3 || len(cleanedName) > 20 {
		return false, fmt.Errorf("name length must be between 3 and 20 characters after removing spaces")
	}

	// 检查是否包含特殊字符
	// for _, char := range cleanedName {
	// 	if !unicode.IsLetter(char) && !unicode.IsDigit(char) {
	// 		return false, fmt.Errorf("name can only contain letters and numbers")
	// 	}
	// }

	// 查询数据库中是否已经存在去除空格后相同的名称
	query := fmt.Sprintf(`
		SELECT COUNT(1)
		FROM %s
		WHERE name = $1;
	`, TableTrainerName)

	var count int
	err := db.QueryRowContext(ctx, query, cleanedName).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check name uniqueness: %v", err)
	}

	// 如果 count 不为 0，说明名称已存在
	if count > 0 {
		return false, fmt.Errorf("name already exists")
	}

	// 名称符合所有规则
	return true, nil
}

func isStarter(name string) (bool, int) {
	generation, exists := starters[name]
	return exists, generation
}

func userTrainerCount(db *sql.DB, userId string, tid int64, onlyUid bool) int {
	var query string
	if onlyUid {
		query = fmt.Sprintf("SELECT COUNT(1) FROM %s WHERE uid = $1", TableTrainerName)
	} else {
		query = fmt.Sprintf("SELECT COUNT(1) FROM %s WHERE id = $1 AND uid = $2", TableTrainerName)
	}

	var count int
	var err error
	if onlyUid {
		err = db.QueryRow(query, userId).Scan(&count)
	} else {
		err = db.QueryRow(query, tid, userId).Scan(&count)
	}
	if err != nil {
		log.Printf("没有查询出结果，不存在Trainer: %v", err)
		return 0
	}
	return count
}

func UpdateTrainerPokeIds(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, newPokeIds []string) error {
	// trainer.Lock() // 或全局锁
	// defer trainer.Unlock()
	oldPokeIds := trainer.PokeIds
	trainer.PokeIds = newPokeIds
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.PokeIds = oldPokeIds // 恢复旧的 PokeIds
		logger.Error("UpdateTrainerPokeIds failed to upsert trainer: %v", err)
		return err
	}
	// 标记dirty，后续批量UpsertTrainer
	return nil
}

// 将 Trainer 数据插入或更新到数据库中，支持事务
func UpsertTrainer(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer) (int64, error) {
	trainer.UpdateTs = time.Now().UnixMilli()
	tableName := TableTrainerName

	if trainer.Id < 0 {
		err := tx.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval(pg_get_serial_sequence('%s', 'id'))", tableName)).Scan(&trainer.Id)
		if err != nil {
			return 0, fmt.Errorf("failed to generate new id: %v", err)
		}
	}

	updateSQL := fmt.Sprintf(`
        INSERT INTO %s (
            id, uid, name, gender, action_info, poke_ids, items, badges, team,
            group_id, cloth, coin, team_info, special_coin, follow_poke,
            create_ts, update_ts, online_time, special_right, box_status, decoration, strict_info
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9,
            $10, $11, $12, $13, $14, $15,
            $16, $17, $18, $19, $20, $21, $22
        )
        ON CONFLICT (id) DO UPDATE SET
            uid = EXCLUDED.uid,
            name = EXCLUDED.name,
            gender = EXCLUDED.gender,
            action_info = EXCLUDED.action_info,
            poke_ids = EXCLUDED.poke_ids,
            items = EXCLUDED.items,
            badges = EXCLUDED.badges,
            team = EXCLUDED.team,
            group_id = EXCLUDED.group_id,
            cloth = EXCLUDED.cloth,
            coin = EXCLUDED.coin,
            team_info = EXCLUDED.team_info,
            special_coin = EXCLUDED.special_coin,
            follow_poke = EXCLUDED.follow_poke,
            create_ts = EXCLUDED.create_ts,
            update_ts = EXCLUDED.update_ts,
            online_time = EXCLUDED.online_time,
            special_right = EXCLUDED.special_right,
            box_status = EXCLUDED.box_status,
            decoration = EXCLUDED.decoration,
            strict_info = EXCLUDED.strict_info
        RETURNING id;
    `, tableName)

	// 序列化字段
	itemsJSON, err := json.Marshal(trainer.Items)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize items: %v", err)
	}

	clothJSON, err := json.Marshal(trainer.Cloth)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize cloth: %v", err)
	}

	teamInfoJSON, err := json.Marshal(trainer.TeamInfo)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize team_info: %v", err)
	}

	followPokeJSON, err := json.Marshal(trainer.FollowPoke)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize follow_poke: %v", err)
	}

	badgesJSON, err := json.Marshal(trainer.Badges)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize badges: %v", err)
	}

	boxStatusJSON, err := json.Marshal(trainer.BoxStatus)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize box_status: %v", err)
	}

	decorationJSON, err := json.Marshal(trainer.Decoration)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize decoration: %v", err)
	}

	strictInfoJSON, err := json.Marshal(trainer.StrictInfo)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize strict_info: %v", err)
	}

	actionInfoJSON, err := json.Marshal(trainer.ActionInfo)
	if err != nil {
		return 0, fmt.Errorf("failed to serialize action_info: %v", err)
	}

	var id int64
	err = tx.QueryRowContext(ctx, updateSQL,
		trainer.Id, trainer.Uid, trainer.Name, trainer.Gender, actionInfoJSON,
		strings.Join(trainer.PokeIds, ","), itemsJSON,
		badgesJSON, trainer.Team, trainer.GroupId,
		clothJSON, trainer.Coin, teamInfoJSON, trainer.SpecialCoin,
		followPokeJSON, trainer.CreateTs, trainer.UpdateTs, trainer.OnlineTime,
		trainer.SpecialRight, boxStatusJSON, decorationJSON, strictInfoJSON,
	).Scan(&id)
	if err != nil {
		return 0, fmt.Errorf("failed to upsert trainer: %v", err)
	}

	return id, nil
}

func createTrainerTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) error {
	tableName := TableTrainerName

	createTableSQL := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            uid VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            gender VARCHAR(5) NOT NULL,
            action_info JSONB NOT NULL DEFAULT '{}'::jsonb,
            poke_ids VARCHAR(255),
            items JSONB NOT NULL DEFAULT '{}'::jsonb,
            badges JSONB NOT NULL DEFAULT '{}'::jsonb,
            team INT NOT NULL DEFAULT 0,
            group_id VARCHAR(255),
            cloth JSONB NOT NULL DEFAULT '{}'::jsonb,
            coin BIGINT DEFAULT 0,
            team_info JSONB NOT NULL DEFAULT '{}'::jsonb,
            special_coin BIGINT DEFAULT 0,
            follow_poke JSONB NOT NULL DEFAULT '{}'::jsonb,
            create_ts BIGINT,
            update_ts BIGINT,
            online_time BIGINT,
            special_right INT NOT NULL DEFAULT 0,
            box_status JSONB NOT NULL DEFAULT '{}'::jsonb,
            decoration JSONB NOT NULL DEFAULT '{}'::jsonb,
            strict_info JSONB NOT NULL DEFAULT '{}'::jsonb
        );
    `, tableName)

	_, err := db.ExecContext(ctx, createTableSQL)
	if err != nil {
		logger.Error("failed to create table %s: %v", tableName, err)
		return fmt.Errorf("failed to create table %s: %v", tableName, err)
	}

	// // 检查并添加decoration字段（如果表已存在但没有decoration字段）
	// alterTableSQL := fmt.Sprintf(`
	// 	ALTER TABLE %s
	// 	ADD COLUMN IF NOT EXISTS decoration JSONB NOT NULL DEFAULT '{}'::jsonb;
	// `, tableName)

	// _, err = db.ExecContext(ctx, alterTableSQL)
	// if err != nil {
	// 	logger.Error("failed to add decoration column to table %s: %v", tableName, err)
	// 	// 不返回错误，因为字段可能已经存在
	// } else {
	// 	logger.Info("Successfully added decoration column to trainer table")
	// }

	logger.Info("Trainer table created successfully.")
	return nil
}

// scanTrainer 从数据库行中读取数据并解析为 Trainer 对象
func scanTrainer(scanner interface {
	Scan(dest ...interface{}) error
}) (*MainServer.Trainer, error) {
	var (
		trainer        MainServer.Trainer
		pokeIDs        string
		actionInfoJSON []byte
		itemsJSON      []byte
		badgesJSON     []byte
		clothJSON      []byte
		teamInfoJSON   []byte
		followPokeJSON []byte
		boxStatusJSON  []byte
		decorationJSON []byte
		strictInfoJSON []byte
	)

	err := scanner.Scan(
		&trainer.Id, &trainer.Uid, &trainer.Name, &trainer.Gender, &actionInfoJSON,
		&pokeIDs, &itemsJSON, &badgesJSON, &trainer.Team,
		&trainer.GroupId, &clothJSON, &trainer.Coin, &teamInfoJSON,
		&trainer.SpecialCoin, &followPokeJSON, &trainer.CreateTs,
		&trainer.UpdateTs, &trainer.OnlineTime, &trainer.SpecialRight, &boxStatusJSON,
		&decorationJSON, &strictInfoJSON,
	)
	if err != nil {
		return nil, err
	}

	// 解析 JSON 字段
	if err := json.Unmarshal(itemsJSON, &trainer.Items); err != nil {
		return nil, fmt.Errorf("failed to parse items JSON: %v", err)
	}
	if err := json.Unmarshal(clothJSON, &trainer.Cloth); err != nil {
		return nil, fmt.Errorf("failed to parse cloth JSON: %v", err)
	}
	if err := json.Unmarshal(teamInfoJSON, &trainer.TeamInfo); err != nil {
		return nil, fmt.Errorf("failed to parse team_info JSON: %v", err)
	}
	if err := json.Unmarshal(followPokeJSON, &trainer.FollowPoke); err != nil {
		return nil, fmt.Errorf("failed to parse follow_poke JSON: %v", err)
	}
	if err := json.Unmarshal(badgesJSON, &trainer.Badges); err != nil {
		return nil, fmt.Errorf("failed to parse special_right JSON: %v", err)
	}
	if err := json.Unmarshal(boxStatusJSON, &trainer.BoxStatus); err != nil {
		return nil, fmt.Errorf("failed to parse box_status JSON: %v", err)
	}
	if err := json.Unmarshal(decorationJSON, &trainer.Decoration); err != nil {
		return nil, fmt.Errorf("failed to parse decoration JSON: %v", err)
	}
	if err := json.Unmarshal(strictInfoJSON, &trainer.StrictInfo); err != nil {
		return nil, fmt.Errorf("failed to parse strict_info JSON: %v", err)
	}
	if err := json.Unmarshal(actionInfoJSON, &trainer.ActionInfo); err != nil {
		return nil, fmt.Errorf("failed to parse action_info JSON: %v", err)
	}

	// 解析其他字段
	trainer.PokeIds = strings.Split(pokeIDs, ",")

	return &trainer, nil
}

// SelectTrainerProto 根据 trainerId 查询并返回单个 Trainer 的 Proto 对象
func SelectTrainerProto(ctx context.Context, db *sql.DB, trainerId int64) (*MainServer.Trainer, error) {
	query := fmt.Sprintf(`
        SELECT id, uid, name, gender, action_info, poke_ids, items, badges, team,
               group_id, cloth, coin, team_info, special_coin, follow_poke,
               create_ts, update_ts, online_time, special_right, box_status, decoration, strict_info
        FROM %s WHERE id = $1`, TableTrainerName)

	row := db.QueryRowContext(ctx, query, trainerId)

	trainer, err := scanTrainer(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("trainer with id %d not found", trainerId)
		}
		return nil, fmt.Errorf("failed to retrieve trainer: %v", err)
	}

	// 初始化 sessionInfo（不存储在数据库中的临时数据）
	trainer.SessionInfo = &MainServer.TrainerSessionInfo{}

	// 确保 StrictInfo 已初始化
	if trainer.StrictInfo == nil {
		trainer.StrictInfo = &MainServer.TrainerStrictInfo{}
	}

	// 确保 ActionInfo 已初始化
	if trainer.ActionInfo == nil {
		trainer.ActionInfo = &MainServer.TrainerActionInfo{}
	}
	if trainer.ActionInfo.LastMainLand == nil {
		trainer.ActionInfo.LastMainLand = &MainServer.TrainerLastMainLandLocInfo{}
	}
	return trainer, nil
}

func SelectTrainerProtoByTx(ctx context.Context, tx *sql.Tx, trainerId int64) (*MainServer.Trainer, error) {
	query := fmt.Sprintf(`
        SELECT id, uid, name, gender, action_info, poke_ids, items, badges, team,
               group_id, cloth, coin, team_info, special_coin, follow_poke,
               create_ts, update_ts, online_time, special_right, box_status, decoration, strict_info
        FROM %s WHERE id = $1`, TableTrainerName)

	row := tx.QueryRowContext(ctx, query, trainerId)

	trainer, err := scanTrainer(row)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("trainer with id %d not found", trainerId)
		}
		return nil, fmt.Errorf("failed to retrieve trainer: %v", err)
	}

	// 初始化 sessionInfo（不存储在数据库中的临时数据）
	trainer.SessionInfo = &MainServer.TrainerSessionInfo{}

	// 确保 StrictInfo 已初始化
	if trainer.StrictInfo == nil {
		trainer.StrictInfo = &MainServer.TrainerStrictInfo{}
	}

	// 确保 ActionInfo 已初始化
	if trainer.ActionInfo == nil {
		trainer.ActionInfo = &MainServer.TrainerActionInfo{}
	}
	if trainer.ActionInfo.LastMainLand == nil {
		trainer.ActionInfo.LastMainLand = &MainServer.TrainerLastMainLandLocInfo{}
	}
	return trainer, nil
}

// 从数据库中查询并返回Trainer的Proto对象
// func SelectTrainerProto(ctx context.Context, db *sql.DB, trainerId int64) (*MainServer.Trainer, error) {
// 	query := fmt.Sprintf("SELECT id, uid, name, loc, poke_ids, action, items, badges, team, group_id, cloth FROM %s WHERE id = $1", TableTrainerName)

// 	row := db.QueryRowContext(ctx, query, trainerId)

// SelectTrainersByUID 根据 UID 查询并返回多个 Trainer 的 Proto 对象切片
func SelectTrainersByUID(ctx context.Context, db *sql.DB, uid string) ([]*MainServer.Trainer, error) {
	query := fmt.Sprintf(`
        SELECT id, uid, name, gender, action_info, poke_ids, items, badges, team,
               group_id, cloth, coin, team_info, special_coin, follow_poke,
               create_ts, update_ts, online_time, special_right, box_status, decoration, strict_info
        FROM %s WHERE uid = $1`, TableTrainerName)

	rows, err := db.QueryContext(ctx, query, uid)
	if err != nil {
		return nil, fmt.Errorf("failed to query trainers: %v", err)
	}
	defer rows.Close()

	var trainers []*MainServer.Trainer
	for rows.Next() {
		trainer, err := scanTrainer(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trainer: %v", err)
		}
		// 初始化 sessionInfo（不存储在数据库中的临时数据）
		trainer.SessionInfo = &MainServer.TrainerSessionInfo{}

		// 确保 StrictInfo 已初始化
		if trainer.StrictInfo == nil {
			trainer.StrictInfo = &MainServer.TrainerStrictInfo{}
		}

		// 确保 ActionInfo 已初始化
		if trainer.ActionInfo == nil {
			trainer.ActionInfo = &MainServer.TrainerActionInfo{}
		}

		trainers = append(trainers, trainer)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during rows iteration: %v", err)
	}

	return trainers, nil
}

// SelectTrainersByIDs 根据多个 ID 查询并返回多个 Trainer 的 Proto 对象切片
func selectTrainersByIDs(ctx context.Context, db *sql.DB, ids []int64) ([]*MainServer.Trainer, error) {
	if len(ids) == 0 {
		return []*MainServer.Trainer{}, nil
	}

	// 构建 IN 查询的占位符
	placeholders := make([]string, len(ids))
	args := make([]interface{}, len(ids))
	for i, id := range ids {
		placeholders[i] = fmt.Sprintf("$%d", i+1)
		args[i] = id
	}

	query := fmt.Sprintf(`
        SELECT id, uid, name, gender, action_info, poke_ids, items, badges, team,
               group_id, cloth, coin, team_info, special_coin, follow_poke,
               create_ts, update_ts, online_time, special_right, box_status, decoration, strict_info
        FROM %s WHERE id IN (%s)`, TableTrainerName, strings.Join(placeholders, ","))

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query trainers by IDs: %v", err)
	}
	defer rows.Close()

	var trainers []*MainServer.Trainer
	for rows.Next() {
		trainer, err := scanTrainer(rows)
		if err != nil {
			return nil, fmt.Errorf("failed to scan trainer: %v", err)
		}
		// 初始化 sessionInfo（不存储在数据库中的临时数据）
		trainer.SessionInfo = &MainServer.TrainerSessionInfo{}

		// 确保 StrictInfo 已初始化
		if trainer.StrictInfo == nil {
			trainer.StrictInfo = &MainServer.TrainerStrictInfo{}
		}

		// 确保 ActionInfo 已初始化
		if trainer.ActionInfo == nil {
			trainer.ActionInfo = &MainServer.TrainerActionInfo{}
		}

		trainers = append(trainers, trainer)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during rows iteration: %v", err)
	}

	return trainers, nil
}

func selectTrainerMapByTrainerId(ctx context.Context, db *sql.DB, trainerId string) ([]map[string]interface{}, error) {
	query := fmt.Sprintf("SELECT * FROM %s WHERE id = '%s'", TableTrainerName, trainerId)
	return tool.RetrieveMap(ctx, db, query)
}

func selectTrainerMapByUId(ctx context.Context, db *sql.DB, uid string) ([]map[string]interface{}, error) {
	query := fmt.Sprintf("SELECT * FROM %s WHERE uid = '%s'", TableTrainerName, uid)
	return tool.RetrieveMap(ctx, db, query)
}

func GetAllTrainerIds(ctx context.Context, tx *sql.Tx) ([]int64, error) {
	// 获取所有活跃的训练师
	query := fmt.Sprintf(`
		SELECT id 
		FROM %s
	`, TableTrainerName)

	rows, err := tx.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("查询所有训练师失败: %v", err)
	}
	defer rows.Close()

	var receiverIds []int64
	for rows.Next() {
		var id int64
		if err := rows.Scan(&id); err != nil {
			continue
		}
		receiverIds = append(receiverIds, id)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历训练师列表失败: %v", err)
	}

	return receiverIds, nil
}
func expendCoin(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.Coin < amount {
		return runtime.NewError("trainer coin not enough", 400)
	}
	oldCoin := trainer.Coin
	trainer.Coin -= amount
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		logger.Error("expendCoin failed to upsert trainer: %v", err)
		trainer.Coin = oldCoin
	}
	return err
}
func expendSpecialCoin(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.SpecialCoin < amount {
		return runtime.NewError("trainer special coin not enough", 400)
	}
	oldSpecialCoin := trainer.SpecialCoin
	trainer.SpecialCoin -= amount
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		logger.Error("expendSpecialCoin failed to upsert trainer: %v", err)
		trainer.SpecialCoin = oldSpecialCoin
	}
	return err
}
func expendTeamContribution(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if trainer.TeamInfo.Contribution < amount {
		return runtime.NewError("trainer team contribution not enough", 400)
	}
	oldContribution := trainer.TeamInfo.Contribution
	trainer.TeamInfo.Contribution -= amount
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		logger.Error("expendTeamContribution failed to upsert trainer: %v", err)
		trainer.TeamInfo.Contribution = oldContribution
	}
	return err
}
func addCoin(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if amount <= 0 {
		return runtime.NewError("trainer coin amount must be positive", 400)
	}
	oldCoin := trainer.Coin
	trainer.Coin += amount
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		logger.Error("addCoin failed to upsert trainer: %v", err)
		trainer.Coin = oldCoin
	}
	return err
}
func addSpecialCoin(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	if amount <= 0 {
		return runtime.NewError("trainer special coin amount must be positive", 400)
	}
	oldSpecialCoin := trainer.SpecialCoin
	trainer.SpecialCoin += amount
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		logger.Error("addSpecialCoin failed to upsert trainer: %v", err)
		trainer.SpecialCoin = oldSpecialCoin
	}
	return err
}
func addTeamContribution(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, amount int64) error {
	trainer.TeamInfo.Contribution += amount
	_, err := UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		trainer.TeamInfo.Contribution -= amount // 恢复原来的团队贡献值
		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
		return err
	}
	return nil
}

//	func expendTeamContribution(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, teamType MainServer.TrainerTeam, amount int64) error {
//		teamInfo, exists := trainer.TeamInfo.TeamInfo[int32(teamType.Number())]
//		if !exists {
//			return runtime.NewError("trainer team info not found", 400)
//		}
//		if teamInfo.Contribution < amount {
//			return runtime.NewError("trainer team contribution not enough", 400)
//		}
//		// 更新团队贡献值
//		teamInfo.Contribution -= amount
//		// 更新团队信息
//		trainer.TeamInfo.TeamInfo[int32(teamType.Number())] = teamInfo
//		// 执行更新操作
//		_, err := UpsertTrainer(ctx, logger, tx, trainer)
//		if err != nil {
//			logger.Error("expendTeamContribution failed to upsert trainer: %v", err)
//			// 如果更新失败，恢复原来的团队贡献值
//			teamInfo.Contribution += amount
//			trainer.TeamInfo.TeamInfo[int32(teamType.Number())] = teamInfo
//		}
//		return err
//	}
// func addTeamContribution(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, teamType MainServer.TrainerTeam, amount int64) error {
// 	teamInfo, exists := trainer.TeamInfo.TeamInfo[int32(teamType.Number())]
// 	if !exists {
// 		teamInfo = &MainServer.TrainerTeamInfoDetail{
// 			Contribution: 0,
// 			TeamType:     teamType,
// 			Level:        0,
// 		}
// 		// return runtime.NewError("trainer team info not found", 400)
// 	}
// 	teamInfo.Contribution += amount
// 	trainer.TeamInfo.TeamInfo[int32(teamType.Number())] = teamInfo
// 	_, err := UpsertTrainer(ctx, logger, tx, trainer)
// 	if err != nil {
// 		logger.Error("addTeamContribution failed to upsert trainer: %v", err)
// 		return err
// 	}
// 	return nil
// }
