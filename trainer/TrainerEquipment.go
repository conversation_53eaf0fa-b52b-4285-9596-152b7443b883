package trainer

import (
	"context"
	"database/sql"
	"fmt"
	"go-nakama-poke/equipment"
	"go-nakama-poke/proto/MainServer"

	"github.com/heroiclabs/nakama-common/runtime"
)

// InitTrainerEquipment 初始化训练师装备模块
func InitTrainerEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	equipment.InitEquipment(ctx, logger, db)
}

// EquipToTrainerDecoration 装备到训练师装饰
func EquipToTrainerDecoration(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, equipmentId int64, equipmentType MainServer.EquipmentType) error {
	// 获取装备信息
	equipmentInfo, err := equipment.EquipToTrainerDecoration(ctx, tx, trainer.Id, equipmentId, equipmentType)
	if err != nil {
		return err
	}

	// 确保装饰对象存在
	if trainer.Decoration == nil {
		trainer.Decoration = &MainServer.TrainerDecoration{}
	}

	// 根据装备类型装备到对应位置
	switch equipmentType {
	case MainServer.EquipmentType_equipment_title:
		trainer.Decoration.EquipmentTitle = equipmentInfo
	case MainServer.EquipmentType_equipment_card:
		trainer.Decoration.EquipmentCard = equipmentInfo
	case MainServer.EquipmentType_equipment_ride:
		trainer.Decoration.EquipmentRide = equipmentInfo
	case MainServer.EquipmentType_equipment_pokeball:
		trainer.Decoration.EquipmentPokeball = equipmentInfo
	case MainServer.EquipmentType_equipment_badge:
		trainer.Decoration.EquipmentBadge = equipmentInfo
	case MainServer.EquipmentType_equipment_amulet:
		trainer.Decoration.EquipmentAmulet = equipmentInfo
	default:
		return fmt.Errorf("不支持的装备类型: %v", equipmentType)
	}

	// 保存训练师信息
	_, err = UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		return fmt.Errorf("保存训练师装备信息失败: %w", err)
	}

	return nil
}

// UnequipFromTrainerDecoration 从训练师装饰卸下装备
func UnequipFromTrainerDecoration(ctx context.Context, logger runtime.Logger, tx *sql.Tx, trainer *MainServer.Trainer, equipmentType MainServer.EquipmentType) error {
	// 确保装饰对象存在
	if trainer.Decoration == nil {
		return fmt.Errorf("训练师没有装备任何装饰")
	}

	var equipmentId int64

	// 根据装备类型卸下对应位置的装备
	switch equipmentType {
	case MainServer.EquipmentType_equipment_title:
		if trainer.Decoration.EquipmentTitle != nil {
			equipmentId = trainer.Decoration.EquipmentTitle.Id
			trainer.Decoration.EquipmentTitle = nil
		}
	case MainServer.EquipmentType_equipment_card:
		if trainer.Decoration.EquipmentCard != nil {
			equipmentId = trainer.Decoration.EquipmentCard.Id
			trainer.Decoration.EquipmentCard = nil
		}
	case MainServer.EquipmentType_equipment_ride:
		if trainer.Decoration.EquipmentRide != nil {
			equipmentId = trainer.Decoration.EquipmentRide.Id
			trainer.Decoration.EquipmentRide = nil
		}
	case MainServer.EquipmentType_equipment_pokeball:
		if trainer.Decoration.EquipmentPokeball != nil {
			equipmentId = trainer.Decoration.EquipmentPokeball.Id
			trainer.Decoration.EquipmentPokeball = nil
		}
	case MainServer.EquipmentType_equipment_badge:
		if trainer.Decoration.EquipmentBadge != nil {
			equipmentId = trainer.Decoration.EquipmentBadge.Id
			trainer.Decoration.EquipmentBadge = nil
		}
	case MainServer.EquipmentType_equipment_amulet:
		if trainer.Decoration.EquipmentAmulet != nil {
			equipmentId = trainer.Decoration.EquipmentAmulet.Id
			trainer.Decoration.EquipmentAmulet = nil
		}
	default:
		return fmt.Errorf("不支持的装备类型: %v", equipmentType)
	}

	if equipmentId == 0 {
		return fmt.Errorf("该位置没有装备")
	}

	// 检查装备是否存在
	err := equipment.UnequipFromTrainerDecoration(ctx, tx, trainer.Id, equipmentId)
	if err != nil {
		return err
	}

	// 保存训练师信息
	_, err = UpsertTrainer(ctx, logger, tx, trainer)
	if err != nil {
		return fmt.Errorf("保存训练师装备信息失败: %w", err)
	}

	return nil
}

// GetTrainerEquipments 获取训练师的所有装备
func GetTrainerEquipments(ctx context.Context, tx *sql.Tx, tid int64, updateTs int64) ([]*MainServer.Equipment, error) {
	return equipment.GetTrainerEquipments(ctx, tx, tid, updateTs)
}

// AddTrainerEquipment 添加训练师装备
func AddTrainerEquipment(ctx context.Context, tx *sql.Tx, equipmentData *MainServer.Equipment) error {
	return equipment.AddEquipment(ctx, tx, equipmentData)
}

// UpdateTrainerEquipment 更新训练师装备
func UpdateTrainerEquipment(ctx context.Context, tx *sql.Tx, equipmentData *MainServer.Equipment) error {
	return equipment.UpdateEquipment(ctx, tx, equipmentData)
}

// DeleteTrainerEquipment 删除训练师装备
func DeleteTrainerEquipment(ctx context.Context, tx *sql.Tx, equipmentId int64) error {
	return equipment.DeleteEquipment(ctx, tx, equipmentId)
}

// QueryTrainerEquipments 查询训练师装备
func QueryTrainerEquipments(ctx context.Context, tx *sql.Tx, filter *MainServer.EquipmentFilter) ([]*MainServer.Equipment, error) {
	return equipment.QueryEquipments(ctx, tx, filter)
}

// SaleTrainerEquipment 上架训练师装备
func SaleTrainerEquipment(ctx context.Context, tx *sql.Tx, tid int64, param *MainServer.SaleEquipmentParam) error {
	return equipment.SaleEquipment(ctx, tx, tid, param)
}

// UnsaleTrainerEquipment 下架训练师装备
func UnsaleTrainerEquipment(ctx context.Context, tx *sql.Tx, tid int64, param *MainServer.UnsaleEquipmentParam) error {
	return equipment.UnsaleEquipment(ctx, tx, tid, param)
}

// FortifyTrainerEquipment 强化训练师装备
func FortifyTrainerEquipment(ctx context.Context, tx *sql.Tx, equipmentId int64, fortifyCount int32) error {
	return equipment.FortifyEquipment(ctx, tx, equipmentId, fortifyCount)
}

// GetEquippedItems 获取已装备的装备信息
func GetEquippedItems(trainer *MainServer.Trainer) map[MainServer.EquipmentType]*MainServer.TrainerEquipmentInfo {
	result := make(map[MainServer.EquipmentType]*MainServer.TrainerEquipmentInfo)

	if trainer.Decoration == nil {
		return result
	}

	if trainer.Decoration.EquipmentTitle != nil {
		result[MainServer.EquipmentType_equipment_title] = trainer.Decoration.EquipmentTitle
	}
	if trainer.Decoration.EquipmentCard != nil {
		result[MainServer.EquipmentType_equipment_card] = trainer.Decoration.EquipmentCard
	}
	if trainer.Decoration.EquipmentRide != nil {
		result[MainServer.EquipmentType_equipment_ride] = trainer.Decoration.EquipmentRide
	}
	if trainer.Decoration.EquipmentPokeball != nil {
		result[MainServer.EquipmentType_equipment_pokeball] = trainer.Decoration.EquipmentPokeball
	}
	if trainer.Decoration.EquipmentBadge != nil {
		result[MainServer.EquipmentType_equipment_badge] = trainer.Decoration.EquipmentBadge
	}
	if trainer.Decoration.EquipmentAmulet != nil {
		result[MainServer.EquipmentType_equipment_amulet] = trainer.Decoration.EquipmentAmulet
	}

	return result
}

// CalculateEquipmentEffects 计算装备效果总和
func CalculateEquipmentEffects(trainer *MainServer.Trainer) *MainServer.TrainerEquipmentAllOneEffect {
	totalEffect := &MainServer.TrainerEquipmentAllOneEffect{}
	totalEffect.EffectPokeTypeInfo = &MainServer.TrainerEquipmentEffectPokeAllOneTypeInfo{}
	if trainer.Decoration == nil {
		return totalEffect
	}

	// 累加所有装备的效果
	equipments := []*MainServer.TrainerEquipmentInfo{
		trainer.Decoration.EquipmentTitle,
		trainer.Decoration.EquipmentCard,
		trainer.Decoration.EquipmentRide,
		trainer.Decoration.EquipmentPokeball,
		trainer.Decoration.EquipmentBadge,
		trainer.Decoration.EquipmentAmulet,
	}

	for _, eq := range equipments {
		if eq != nil && eq.EffectInfo != nil {
			for effectType, effectValue := range eq.EffectInfo.EquipmentEffect {
				// totalEffect.EquipmentEffect[effectType] += effectValue
				switch (MainServer.TrainerEquipmentEffectType)(effectType) {
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterEnemy:
					totalEffect.EncounterEnemy += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterShine:
					totalEffect.EncounterShine += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BreedGoodPokebaby:
					totalEffect.BreedGoodPokebaby += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BreedDifficultBaby:
					totalEffect.BreedDifficultBaby += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BorrowGoodPoke:
					totalEffect.BorrowGoodPoke += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_FishingGoodEquipment:
					totalEffect.FishingGoodEquipment += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_FishingGoodPoke:
					totalEffect.FishingGoodPoke += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_EncounterGoodEnemy:
					totalEffect.EncounterGoodEnemy += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattlePoke:
					totalEffect.GetBattlePoke += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetWildBattleEquipment:
					totalEffect.GetWildBattleEquipment += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_TreeFruitGrowup:
					totalEffect.TreeFruitGrowup += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_TreeFruitWithered:
					totalEffect.TreeFruitWithered += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_HatchEggTime:
					totalEffect.HatchEggTime += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_FishingBattleEquipment:
					totalEffect.FishingBattleEquipment += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattleExp:
					totalEffect.GetBattleExp += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattleMoney:
					totalEffect.GetBattleMoney += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_GetBattleEvs:
					totalEffect.GetBattleEvs += effectValue
					break
				case MainServer.TrainerEquipmentEffectType_EquipmentEffect_BreedShine:
					totalEffect.BreedShine += effectValue
					break
				}
			}
			for effectPokeType, effectValue := range eq.EffectInfo.EquipmentPokeEffect {
				// totalEffect.EquipmentPokeEffect[effectPokeType] += effectValue
				switch (MainServer.TrainerEquipmentEffectPokeType)(effectPokeType) {
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Normal:
					totalEffect.EffectPokeTypeInfo.EffectPokeNormal += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fire:
					totalEffect.EffectPokeTypeInfo.EffectPokeFire += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Water:
					totalEffect.EffectPokeTypeInfo.EffectPokeWater += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Electric:
					totalEffect.EffectPokeTypeInfo.EffectPokeElectric += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Grass:
					totalEffect.EffectPokeTypeInfo.EffectPokeGrass += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ice:
					totalEffect.EffectPokeTypeInfo.EffectPokeIce += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fighting:
					totalEffect.EffectPokeTypeInfo.EffectPokeFighting += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Poison:
					totalEffect.EffectPokeTypeInfo.EffectPokePoison += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ground:
					totalEffect.EffectPokeTypeInfo.EffectPokeGround += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Flying:
					totalEffect.EffectPokeTypeInfo.EffectPokeFlying += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Psychic:
					totalEffect.EffectPokeTypeInfo.EffectPokePsychic += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Bug:
					totalEffect.EffectPokeTypeInfo.EffectPokeBug += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Rock:
					totalEffect.EffectPokeTypeInfo.EffectPokeRock += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Ghost:
					totalEffect.EffectPokeTypeInfo.EffectPokeGhost += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Dragon:
					totalEffect.EffectPokeTypeInfo.EffectPokeDragon += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Dark:
					totalEffect.EffectPokeTypeInfo.EffectPokeDark += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Steel:
					totalEffect.EffectPokeTypeInfo.EffectPokeSteel += effectValue
					break
				case MainServer.TrainerEquipmentEffectPokeType_EffectPoke_Fairy:
					totalEffect.EffectPokeTypeInfo.EffectPokeFairy += effectValue
					break
				}
			}
			// totalEffect.EncounterEnemy += eq.EffectInfo.EncounterEnemy
			// totalEffect.EncounterShine += eq.EffectInfo.EncounterShine
			// totalEffect.BreedGoodPokebaby += eq.EffectInfo.BreedGoodPokebaby
			// totalEffect.BreedDifficultBaby += eq.EffectInfo.BreedDifficultBaby
			// totalEffect.BorrowGoodPoke += eq.EffectInfo.BorrowGoodPoke
			// totalEffect.FishingGoodEquipment += eq.EffectInfo.FishingGoodEquipment
			// totalEffect.FishingGoodPoke += eq.EffectInfo.FishingGoodPoke
			// totalEffect.EncounterGoodEnemy += eq.EffectInfo.EncounterGoodEnemy
			// totalEffect.GetBattlePoke += eq.EffectInfo.GetBattlePoke
			// totalEffect.GetBattleEquipment += eq.EffectInfo.GetBattleEquipment
			// totalEffect.TreeFruitGrowup += eq.EffectInfo.TreeFruitGrowup
			// totalEffect.TreeFruitWithered += eq.EffectInfo.TreeFruitWithered
			// totalEffect.HatchEggTime += eq.EffectInfo.HatchEggTime
			// totalEffect.FishingBattleEquipment += eq.EffectInfo.FishingBattleEquipment
			// totalEffect.GetBattleExp += eq.EffectInfo.GetBattleExp
			// totalEffect.GetBattleMoney += eq.EffectInfo.GetBattleMoney
			// totalEffect.GetBattleEvs += eq.EffectInfo.GetBattleEvs
			// totalEffect.BreedShine += eq.EffectInfo.BreedShine
		}
	}

	return totalEffect
}

// IsEquipmentSlotEmpty 检查装备槽是否为空
func IsEquipmentSlotEmpty(trainer *MainServer.Trainer, equipmentType MainServer.EquipmentType) bool {
	if trainer.Decoration == nil {
		return true
	}

	switch equipmentType {
	case MainServer.EquipmentType_equipment_title:
		return trainer.Decoration.EquipmentTitle == nil
	case MainServer.EquipmentType_equipment_card:
		return trainer.Decoration.EquipmentCard == nil
	case MainServer.EquipmentType_equipment_ride:
		return trainer.Decoration.EquipmentRide == nil
	case MainServer.EquipmentType_equipment_pokeball:
		return trainer.Decoration.EquipmentPokeball == nil
	case MainServer.EquipmentType_equipment_badge:
		return trainer.Decoration.EquipmentBadge == nil
	case MainServer.EquipmentType_equipment_amulet:
		return trainer.Decoration.EquipmentAmulet == nil
	default:
		return true
	}
}
