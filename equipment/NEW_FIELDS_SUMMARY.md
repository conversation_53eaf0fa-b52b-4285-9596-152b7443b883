# 装备系统新增字段总结

## 新增字段

### 1. expired_ts (过期时间戳)
- **类型**: `int64`
- **默认值**: `0`
- **说明**: 装备的过期时间戳，0表示永不过期
- **用途**: 
  - 限制装备的使用时间
  - 过期装备无法装备到装饰槽
  - 可用于限时活动装备

### 2. item_sale_type (道具销售类型)
- **类型**: `ItemSaleType` (枚举)
- **默认值**: `ItemSaleType_Normal` (0)
- **说明**: 标识装备是否为专属道具
- **枚举值**:
  - `ItemSaleType_Normal = 0`: 普通道具
  - `ItemSaleType_Exclusive = 1`: 专属道具

## 数据库变更

### 表结构更新
```sql
-- 添加新字段到现有表
ALTER TABLE equipments ADD COLUMN expired_ts BIGINT NOT NULL DEFAULT 0;
ALTER TABLE equipments ADD COLUMN item_sale_type INTEGER NOT NULL DEFAULT 0;

-- 添加新索引
CREATE INDEX IF NOT EXISTS idx_equipment_expired_ts ON equipments (expired_ts);
CREATE INDEX IF NOT EXISTS idx_equipment_item_sale_type ON equipments (item_sale_type);
```

### 完整表结构
```sql
CREATE TABLE IF NOT EXISTS equipments (
    id BIGSERIAL PRIMARY KEY,
    tid BIGINT NOT NULL,
    equipment_name VARCHAR(50) NOT NULL,
    expired_ts BIGINT NOT NULL DEFAULT 0,        -- 新增字段
    price INT NOT NULL DEFAULT 0,
    team_coin INT NOT NULL DEFAULT 0,
    team_type INT NOT NULL DEFAULT 0,
    status INT NOT NULL DEFAULT 1,
    equipment_type INT NOT NULL DEFAULT 0,
    item_sale_type INT NOT NULL DEFAULT 0,       -- 新增字段
    fortify_count INT NOT NULL DEFAULT 0,
    effect_info JSONB NOT NULL DEFAULT '{}',
    create_ts BIGINT NOT NULL,
    update_ts BIGINT NOT NULL
);
```

## 代码变更

### 1. 数据库操作更新
- ✅ `AddEquipment`: 支持新字段插入
- ✅ `UpdateEquipment`: 支持新字段更新
- ✅ `QueryEquipments`: 支持新字段查询和扫描
- ✅ `GetAllEquipments`: 支持新字段查询和扫描

### 2. 业务逻辑更新
- ✅ `IsEquipmentExpired`: 新增函数检查装备是否过期
- ✅ `GetValidEquipments`: 新增函数获取未过期装备
- ✅ `EquipToTrainerDecoration`: 添加过期检查

### 3. 过滤功能预留
```go
// 在QueryEquipments中预留了过滤条件（待proto更新后启用）
// if filter.ExpiredTs > 0 {
//     conditions = append(conditions, fmt.Sprintf("expired_ts = $%d", paramIndex))
//     args = append(args, filter.ExpiredTs)
//     paramIndex++
// }

// if filter.ItemSaleType != 0 {
//     conditions = append(conditions, fmt.Sprintf("item_sale_type = $%d", paramIndex))
//     args = append(args, int(filter.ItemSaleType))
//     paramIndex++
// }
```

## Proto定义更新

### Equipment消息更新
```protobuf
message Equipment {
    int64 id = 1;
    int64 tid = 2;
    string equipment_name = 3;
    int64 expired_ts = 4;                       // 新增字段
    int64 price = 5;
    int64 team_coin = 6;
    TrainerTeam team_type = 7;
    EquipmentStatus status = 8;
    EquipmentType equipment_type = 9;
    ItemSaleType item_sale_type = 10;           // 新增字段
    int32 fortify_count = 11;
    TrainerEquipmentEffect effect_info = 12;
    int64 create_ts = 13;
    int64 update_ts = 14;
}
```

### 新增枚举
```protobuf
enum ItemSaleType {
    ItemSaleType_Normal = 0;                    // 普通道具
    ItemSaleType_Exclusive = 1;                 // 专属道具
}
```

### EquipmentFilter更新
```protobuf
message EquipmentFilter {
    // ... 现有字段 ...
    int64 expired_ts = 12;                      // 过期时间戳过滤
    ItemSaleType item_sale_type = 13;           // 道具销售类型过滤
    bool include_expired = 14;                  // 是否包含已过期装备
}
```

## 功能特性

### 过期装备管理
1. **过期检查**: 装备装饰时自动检查是否过期
2. **过期过滤**: 查询时可选择是否包含过期装备
3. **过期状态**: 提供`IsEquipmentExpired`函数检查过期状态
4. **有效装备**: 提供`GetValidEquipments`函数获取未过期装备

### 专属道具系统
1. **类型标识**: 通过`item_sale_type`字段标识装备类型
2. **权限控制**: 可基于此字段实现专属道具的使用限制
3. **过滤查询**: 支持按道具类型过滤查询

## 使用示例

### 创建限时装备
```go
equipment := &MainServer.Equipment{
    Tid:           trainerId,
    EquipmentName: "限时称号",
    ExpiredTs:     time.Now().AddDate(0, 0, 30).Unix(), // 30天后过期
    ItemSaleType:  MainServer.ItemSaleType_ItemSaleType_Exclusive,
    EquipmentType: MainServer.EquipmentType_equipment_title,
    EffectInfo:    &MainServer.TrainerEquipmentEffect{...},
}
```

### 检查装备是否过期
```go
if equipment.IsEquipmentExpired(equipment) {
    return fmt.Errorf("装备已过期")
}
```

### 获取未过期装备
```go
filter := &MainServer.EquipmentFilter{
    Tid: trainerId,
    // IncludeExpired: false, // 待proto更新后启用
}
validEquipments, err := equipment.GetValidEquipments(ctx, tx, filter)
```

## 向后兼容性

### 数据库兼容
- ✅ 新字段设置了默认值，不影响现有数据
- ✅ 现有装备的`expired_ts`为0（永不过期）
- ✅ 现有装备的`item_sale_type`为0（普通道具）

### 代码兼容
- ✅ 所有现有功能保持不变
- ✅ 新增功能为可选功能
- ✅ 过期检查仅在装备时进行，不影响已装备的装备

## 部署步骤

1. **更新数据库表结构**
   ```sql
   ALTER TABLE equipments ADD COLUMN expired_ts BIGINT NOT NULL DEFAULT 0;
   ALTER TABLE equipments ADD COLUMN item_sale_type INTEGER NOT NULL DEFAULT 0;
   CREATE INDEX IF NOT EXISTS idx_equipment_expired_ts ON equipments (expired_ts);
   CREATE INDEX IF NOT EXISTS idx_equipment_item_sale_type ON equipments (item_sale_type);
   ```

2. **更新Proto定义**
   - 在另一个项目中添加新的proto字段和枚举

3. **部署代码**
   - 部署更新后的Go代码

4. **测试验证**
   - 测试过期装备功能
   - 测试专属道具功能
   - 验证向后兼容性

## 注意事项

1. **时间戳格式**: 使用Unix时间戳（秒）
2. **过期检查**: 仅在装备时检查，已装备的装备不会自动卸下
3. **索引优化**: 新增字段已添加索引，支持高效查询
4. **Proto更新**: 需要在另一个项目中同步更新proto定义
5. **过滤功能**: 部分过滤功能需要等proto更新后才能启用
