package equipment

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/proto/MainServer"
	"strings"
	"time"

	"github.com/heroiclabs/nakama-common/runtime"
)

const (
	TableEquipmentName = "equipment"
)

// 初始化装备表
func InitEquipment(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	createEquipmentTableIfNotExists(ctx, logger, db)
	LoadEquipmentConfigInfos()
}

// 创建装备表（如果不存在）
func createEquipmentTableIfNotExists(ctx context.Context, logger runtime.Logger, db *sql.DB) {
	query := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s (
            id BIGSERIAL PRIMARY KEY,
            tid BIGINT NOT NULL,
            equipment_name VARCHAR(50) NOT NULL,
            expired_ts BIGINT NOT NULL DEFAULT 0,
            price INT NOT NULL DEFAULT 0,
            team_coin INT NOT NULL DEFAULT 0,
            team_type INT NOT NULL DEFAULT 0,
            status INT NOT NULL DEFAULT 1,
            equipment_type INT NOT NULL DEFAULT 0,
            item_sale_type INT NOT NULL DEFAULT 0,
            fortify_count INT NOT NULL DEFAULT 0,
            effect_info JSONB NOT NULL DEFAULT '{}',
            create_ts BIGINT NOT NULL,
            update_ts BIGINT NOT NULL
        );
        CREATE INDEX IF NOT EXISTS idx_equipment_id ON %s (id);
        CREATE INDEX IF NOT EXISTS idx_equipment_tid ON %s (tid);
        CREATE INDEX IF NOT EXISTS idx_equipment_equipment_name ON %s (equipment_name);
        CREATE INDEX IF NOT EXISTS idx_equipment_expired_ts ON %s (expired_ts);
        CREATE INDEX IF NOT EXISTS idx_equipment_status ON %s (status);
        CREATE INDEX IF NOT EXISTS idx_equipment_equipment_type ON %s (equipment_type);
        CREATE INDEX IF NOT EXISTS idx_equipment_item_sale_type ON %s (item_sale_type);
        CREATE INDEX IF NOT EXISTS idx_equipment_fortify_count ON %s (fortify_count);
        CREATE INDEX IF NOT EXISTS idx_equipment_update_ts ON %s (update_ts);
    `, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName, TableEquipmentName)

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		logger.Error("创建装备表失败: %v", err)
		return
	}

	logger.Info("装备表初始化完成")
}

// 添加装备
func AddEquipment(ctx context.Context, tx *sql.Tx, equipment *MainServer.Equipment) error {
	// 序列化效果信息
	effectInfoBytes, err := json.Marshal(equipment.EffectInfo)
	if err != nil {
		return fmt.Errorf("序列化效果信息失败: %w", err)
	}

	now := time.Now().Unix()
	if equipment.CreateTs == 0 {
		equipment.CreateTs = now
	}
	equipment.UpdateTs = now

	query := fmt.Sprintf(`
		INSERT INTO %s (tid, equipment_name, expired_ts, price, team_coin, team_type, status, equipment_type, item_sale_type, fortify_count, effect_info, create_ts, update_ts)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING id
	`, TableEquipmentName)

	err = tx.QueryRowContext(ctx, query,
		equipment.Tid,
		equipment.EquipmentName,
		equipment.ExpiredTs,
		equipment.Price,
		equipment.TeamCoin,
		int(equipment.TeamType),
		int(equipment.Status),
		int(equipment.EquipmentType),
		int(equipment.ItemSaleType),
		equipment.FortifyCount,
		string(effectInfoBytes),
		equipment.CreateTs,
		equipment.UpdateTs,
	).Scan(&equipment.Id)

	if err != nil {
		return fmt.Errorf("添加装备失败: %w", err)
	}

	return nil
}

// 更新装备
func UpdateEquipment(ctx context.Context, tx *sql.Tx, equipment *MainServer.Equipment) error {
	// 序列化效果信息
	effectInfoBytes, err := json.Marshal(equipment.EffectInfo)
	if err != nil {
		return fmt.Errorf("序列化效果信息失败: %w", err)
	}

	equipment.UpdateTs = time.Now().Unix()

	query := fmt.Sprintf(`
		UPDATE %s SET
			equipment_name = $2,
			expired_ts = $3,
			price = $4,
			team_coin = $5,
			team_type = $6,
			status = $7,
			equipment_type = $8,
			item_sale_type = $9,
			fortify_count = $10,
			effect_info = $11,
			update_ts = $12
		WHERE id = $1
	`, TableEquipmentName)

	result, err := tx.ExecContext(ctx, query,
		equipment.Id,
		equipment.EquipmentName,
		equipment.ExpiredTs,
		equipment.Price,
		equipment.TeamCoin,
		int(equipment.TeamType),
		int(equipment.Status),
		int(equipment.EquipmentType),
		int(equipment.ItemSaleType),
		equipment.FortifyCount,
		string(effectInfoBytes),
		equipment.UpdateTs,
	)

	if err != nil {
		return fmt.Errorf("更新装备失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("装备不存在")
	}

	return nil
}

// 删除装备
func DeleteEquipment(ctx context.Context, tx *sql.Tx, equipmentId int64) error {
	query := fmt.Sprintf(`DELETE FROM %s WHERE id = $1`, TableEquipmentName)

	result, err := tx.ExecContext(ctx, query, equipmentId)
	if err != nil {
		return fmt.Errorf("删除装备失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("装备不存在")
	}

	return nil
}

// 查询装备
func QueryEquipments(ctx context.Context, tx *sql.Tx, filter *MainServer.EquipmentFilter) ([]*MainServer.Equipment, error) {
	// 构建查询条件
	conditions := []string{}
	args := []interface{}{}
	paramIndex := 1

	// 如果指定了 id，则只查询该 id
	if filter.Id > 0 {
		conditions = append(conditions, fmt.Sprintf("id = $%d", paramIndex))
		args = append(args, filter.Id)
		paramIndex++
	}

	// tid 是必填的（除非指定了id）
	if filter.Tid > 0 {
		conditions = append(conditions, fmt.Sprintf("tid = $%d", paramIndex))
		args = append(args, filter.Tid)
		paramIndex++
	}
	if filter.EquipmentType != 0 {
		conditions = append(conditions, fmt.Sprintf("equipment_type = $%d", paramIndex))
		args = append(args, int(filter.EquipmentType))
		paramIndex++
	}
	if filter.EquipmentName != "" {
		conditions = append(conditions, fmt.Sprintf("equipment_name = $%d", paramIndex))
		args = append(args, filter.EquipmentName)
		paramIndex++
	}

	if filter.Status != 0 {
		conditions = append(conditions, fmt.Sprintf("status = $%d", paramIndex))
		args = append(args, int(filter.Status))
		paramIndex++
	}

	if filter.FortifyCount > 0 {
		conditions = append(conditions, fmt.Sprintf("fortify_count = $%d", paramIndex))
		args = append(args, filter.FortifyCount)
		paramIndex++
	}

	if filter.UpdateTs > 0 {
		conditions = append(conditions, fmt.Sprintf("update_ts > $%d", paramIndex))
		args = append(args, filter.UpdateTs)
		paramIndex++
	}

	// TODO: 等proto更新后启用这些过滤条件
	// if filter.ExpiredTs > 0 {
	// 	conditions = append(conditions, fmt.Sprintf("expired_ts = $%d", paramIndex))
	// 	args = append(args, filter.ExpiredTs)
	// 	paramIndex++
	// }

	if filter.ItemSaleType != 0 {
		conditions = append(conditions, fmt.Sprintf("item_sale_type = $%d", paramIndex))
		args = append(args, int(filter.ItemSaleType))
		paramIndex++
	}

	// 过期装备过滤（默认不包含已过期的装备）
	// if !filter.IncludeExpired {
	// 	now := time.Now().Unix()
	// 	conditions = append(conditions, fmt.Sprintf("(expired_ts = 0 OR expired_ts > $%d)", paramIndex))
	// 	args = append(args, now)
	// 	paramIndex++
	// }

	// 如果没有任何条件，返回错误
	if len(conditions) == 0 {
		return nil, fmt.Errorf("查询条件不能为空")
	}

	// 构建查询语句
	query := fmt.Sprintf(`
		SELECT id, tid, equipment_name, expired_ts, price, team_coin, team_type, status, equipment_type, item_sale_type, fortify_count, effect_info, create_ts, update_ts
		FROM %s
		WHERE %s
		ORDER BY update_ts DESC
	`, TableEquipmentName, strings.Join(conditions, " AND "))

	// 执行查询
	rows, err := tx.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("查询装备失败: %w", err)
	}
	defer rows.Close()

	// 处理结果
	var equipments []*MainServer.Equipment
	for rows.Next() {
		equipment := &MainServer.Equipment{}
		var statusInt, equipmentTypeInt, teamTypeInt, itemSaleTypeInt int
		var effectInfoStr string

		err := rows.Scan(
			&equipment.Id,
			&equipment.Tid,
			&equipment.EquipmentName,
			&equipment.ExpiredTs,
			&equipment.Price,
			&equipment.TeamCoin,
			&teamTypeInt,
			&statusInt,
			&equipmentTypeInt,
			&itemSaleTypeInt,
			&equipment.FortifyCount,
			&effectInfoStr,
			&equipment.CreateTs,
			&equipment.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描装备数据失败: %w", err)
		}

		equipment.Status = MainServer.EquipmentStatus(statusInt)
		equipment.EquipmentType = MainServer.EquipmentType(equipmentTypeInt)
		equipment.TeamType = MainServer.TrainerTeam(teamTypeInt)
		equipment.ItemSaleType = MainServer.ItemSaleType(itemSaleTypeInt)

		// 反序列化效果信息
		equipment.EffectInfo = &MainServer.TrainerEquipmentEffect{}
		if effectInfoStr != "" && effectInfoStr != "{}" {
			err = json.Unmarshal([]byte(effectInfoStr), equipment.EffectInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化效果信息失败: %w", err)
			}
		}

		equipments = append(equipments, equipment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历装备数据失败: %w", err)
	}

	return equipments, nil
}

// 获取单个装备
func GetEquipmentById(ctx context.Context, tx *sql.Tx, equipmentId int64) (*MainServer.Equipment, error) {
	filter := &MainServer.EquipmentFilter{
		Id: equipmentId,
	}

	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	if len(equipments) == 0 {
		return nil, fmt.Errorf("装备不存在")
	}

	return equipments[0], nil
}

// 获取训练师的所有装备
func GetTrainerEquipments(ctx context.Context, tx *sql.Tx, tid int64, updateTs int64) ([]*MainServer.Equipment, error) {
	filter := &MainServer.EquipmentFilter{
		Tid:      tid,
		UpdateTs: updateTs,
	}

	return QueryEquipments(ctx, tx, filter)
}

// 上架装备
func SaleEquipment(ctx context.Context, tx *sql.Tx, tid int64, param *MainServer.SaleEquipmentParam) error {
	// 查找装备
	filter := &MainServer.EquipmentFilter{
		Tid:           tid,
		EquipmentName: param.EquipmentName,
		Status:        MainServer.EquipmentStatus_EquipmentStatus_Normal,
	}

	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return err
	}

	if len(equipments) == 0 {
		return fmt.Errorf("装备不存在")
	}

	equipment := equipments[0]

	// 更新装备状态和价格
	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Sale
	equipment.Price = param.Price
	equipment.TeamCoin = param.SpecialCoin

	return UpdateEquipment(ctx, tx, equipment)
}

// 下架装备
func UnsaleEquipment(ctx context.Context, tx *sql.Tx, tid int64, param *MainServer.UnsaleEquipmentParam) error {
	// 查找装备
	filter := &MainServer.EquipmentFilter{
		Tid:           tid,
		EquipmentName: param.EquipmentName,
		Status:        MainServer.EquipmentStatus_EquipmentStatus_Sale,
	}

	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return err
	}

	if len(equipments) == 0 {
		return fmt.Errorf("装备不存在")
	}

	equipment := equipments[0]

	// 更新装备状态
	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Normal
	equipment.Price = 0
	equipment.TeamCoin = 0

	return UpdateEquipment(ctx, tx, equipment)
}

// 获取所有装备（管理员用）
func GetAllEquipments(ctx context.Context, tx *sql.Tx, updateTs int64) ([]*MainServer.Equipment, error) {
	query := fmt.Sprintf(`
		SELECT id, tid, equipment_name, expired_ts, price, team_coin, team_type, status, equipment_type, item_sale_type, fortify_count, effect_info, create_ts, update_ts
		FROM %s
		WHERE update_ts > $1
		ORDER BY update_ts DESC
	`, TableEquipmentName)

	rows, err := tx.QueryContext(ctx, query, updateTs)
	if err != nil {
		return nil, fmt.Errorf("查询装备失败: %w", err)
	}
	defer rows.Close()

	var equipments []*MainServer.Equipment
	for rows.Next() {
		equipment := &MainServer.Equipment{}
		var statusInt, equipmentTypeInt, teamTypeInt, itemSaleTypeInt int
		var effectInfoStr string

		err := rows.Scan(
			&equipment.Id,
			&equipment.Tid,
			&equipment.EquipmentName,
			&equipment.ExpiredTs,
			&equipment.Price,
			&equipment.TeamCoin,
			&teamTypeInt,
			&statusInt,
			&equipmentTypeInt,
			&itemSaleTypeInt,
			&equipment.FortifyCount,
			&effectInfoStr,
			&equipment.CreateTs,
			&equipment.UpdateTs,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描装备数据失败: %w", err)
		}

		equipment.Status = MainServer.EquipmentStatus(statusInt)
		equipment.EquipmentType = MainServer.EquipmentType(equipmentTypeInt)
		equipment.TeamType = MainServer.TrainerTeam(teamTypeInt)
		equipment.ItemSaleType = MainServer.ItemSaleType(itemSaleTypeInt)

		// 反序列化效果信息
		equipment.EffectInfo = &MainServer.TrainerEquipmentEffect{}
		if effectInfoStr != "" && effectInfoStr != "{}" {
			err = json.Unmarshal([]byte(effectInfoStr), equipment.EffectInfo)
			if err != nil {
				return nil, fmt.Errorf("反序列化效果信息失败: %w", err)
			}
		}

		equipments = append(equipments, equipment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历装备数据失败: %w", err)
	}

	return equipments, nil
}

// 装备到TrainerDecoration
func EquipToTrainerDecoration(ctx context.Context, tx *sql.Tx, tid int64, equipmentId int64, equipmentType MainServer.EquipmentType) (*MainServer.TrainerEquipmentInfo, error) {
	// 获取装备信息
	equipment, err := GetEquipmentById(ctx, tx, equipmentId)
	if err != nil {
		return nil, err
	}

	// 检查装备是否属于该训练师
	if equipment.Tid != tid {
		return nil, fmt.Errorf("装备不属于该训练师")
	}

	// 检查装备状态
	if equipment.Status != MainServer.EquipmentStatus_EquipmentStatus_Normal {
		return nil, fmt.Errorf("装备状态不正常，无法装备")
	}

	// 检查装备类型是否匹配
	if equipment.EquipmentType != equipmentType {
		return nil, fmt.Errorf("装备类型不匹配")
	}

	// 检查装备是否过期
	if IsEquipmentExpired(equipment) {
		return nil, fmt.Errorf("装备已过期，无法装备")
	}

	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Equip
	err = UpdateEquipment(ctx, tx, equipment)
	if err != nil {
		return nil, err
	}

	// 创建TrainerEquipmentInfo
	equipmentInfo := &MainServer.TrainerEquipmentInfo{
		Id:            equipment.Id,
		EquipmentName: equipment.EquipmentName,
		FortifyCount:  equipment.FortifyCount,
		EffectInfo:    equipment.EffectInfo,
	}

	return equipmentInfo, nil
}

// 卸下装备
func UnequipFromTrainerDecoration(ctx context.Context, tx *sql.Tx, tid int64, equipmentId int64) error {
	// 检查装备是否存在且属于该训练师
	equipment, err := GetEquipmentById(ctx, tx, equipmentId)
	if err != nil {
		return err
	}

	if equipment.Tid != tid {
		return fmt.Errorf("装备不属于该训练师")
	}
	if equipment.Status != MainServer.EquipmentStatus_EquipmentStatus_Equip {
		return fmt.Errorf("装备未装备，无需卸下")
	}
	equipment.Status = MainServer.EquipmentStatus_EquipmentStatus_Normal
	// 装备卸下后不需要特殊处理，只要确保装备存在即可
	return UpdateEquipment(ctx, tx, equipment)
}

// 强化装备
func FortifyEquipment(ctx context.Context, tx *sql.Tx, equipmentId int64, fortifyCount int32) error {
	equipment, err := GetEquipmentById(ctx, tx, equipmentId)
	if err != nil {
		return err
	}

	// 更新强化等级
	equipment.FortifyCount = fortifyCount

	return UpdateEquipment(ctx, tx, equipment)
}

// 批量添加装备
func BatchAddEquipments(ctx context.Context, tx *sql.Tx, equipments []*MainServer.Equipment) error {
	for _, equipment := range equipments {
		err := AddEquipment(ctx, tx, equipment)
		if err != nil {
			return fmt.Errorf("批量添加装备失败，装备: %s, 错误: %w", equipment.EquipmentName, err)
		}
	}
	return nil
}

// 检查装备是否过期
func IsEquipmentExpired(equipment *MainServer.Equipment) bool {
	if equipment.ExpiredTs == 0 {
		return false // 永不过期
	}
	return time.Now().Unix() > equipment.ExpiredTs
}

// 获取未过期的装备
func GetValidEquipments(ctx context.Context, tx *sql.Tx, filter *MainServer.EquipmentFilter) ([]*MainServer.Equipment, error) {
	equipments, err := QueryEquipments(ctx, tx, filter)
	if err != nil {
		return nil, err
	}

	var validEquipments []*MainServer.Equipment
	for _, equipment := range equipments {
		if !IsEquipmentExpired(equipment) {
			validEquipments = append(validEquipments, equipment)
		}
	}

	return validEquipments, nil
}
