# 装备系统 (Equipment System)

## 概述

装备系统是为Pokemon Unity Go Nakama项目设计的训练师装饰系统，允许训练师装备各种类型的装备来获得游戏内的属性加成。

## 功能特性

### 核心功能
- **装备管理**: 增删改查装备
- **装备装饰**: 将装备装备到训练师的装饰槽位
- **装备强化**: 提升装备的强化等级
- **装备交易**: 上架和下架装备进行交易
- **效果计算**: 自动计算装备提供的属性加成

### 装备类型
1. **称号 (Title)**: 显示训练师的成就和地位
2. **卡片 (Card)**: 提供各种属性加成
3. **坐骑 (Ride)**: 影响移动和遭遇
4. **精灵球 (Pokeball)**: 影响捕获相关属性
5. **徽章 (Badge)**: 显示训练师的荣誉
6. **护身符 (Amulet)**: 提供保护性效果

### 装备效果
装备可以提供18种不同的效果加成：
- 遇到闪光宝可梦概率加成
- 孵化相关加成（优质、闪光、速度）
- 捕获成功率加成
- 经验和金币获得加成
- 道具掉落率加成
- 战斗相关加成
- 宝可梦属性加成（HP、攻击、防御、速度、特攻、特防）
- 暴击率加成

### 新增功能
- **装备过期系统**: 装备可以设置过期时间，过期后无法装备使用
- **专属道具标识**: 通过ItemSaleType字段标识装备是否为专属道具

## 数据库设计

### 表结构
```sql
CREATE TABLE equipments (
    id BIGSERIAL PRIMARY KEY,
    tid BIGINT NOT NULL,
    equipment_name VARCHAR(255) NOT NULL,
    expired_ts BIGINT NOT NULL DEFAULT 0,    -- 过期时间戳（0为不过期）
    price BIGINT DEFAULT 0,
    team_coin BIGINT DEFAULT 0,
    team_type INTEGER DEFAULT 0,
    status INTEGER DEFAULT 0,
    equipment_type INTEGER NOT NULL,
    item_sale_type INTEGER NOT NULL DEFAULT 0, -- 道具销售类型（0=普通，1=专属）
    fortify_count INTEGER DEFAULT 0,
    effect_info JSONB,
    create_ts BIGINT NOT NULL,
    update_ts BIGINT NOT NULL
);
```

### 索引
- 主键索引：`id`
- 复合索引：`(tid, equipment_type)` - 用于查询训练师的特定类型装备
- 单列索引：`tid`, `equipment_name`, `expired_ts`, `status`, `equipment_type`, `item_sale_type`, `update_ts`

## API接口

### RPC函数列表

1. **RpcAddEquipment** - 添加装备
2. **RpcUpdateEquipment** - 更新装备
3. **RpcDeleteEquipment** - 删除装备
4. **RpcQueryEquipments** - 查询装备
5. **RpcSaleEquipment** - 上架装备
6. **RpcUnsaleEquipment** - 下架装备
7. **RpcEquipToDecoration** - 装备到装饰槽
8. **RpcUnequipFromDecoration** - 从装饰槽卸下
9. **RpcFortifyEquipment** - 强化装备
10. **RpcGetAllEquipments** - 获取所有装备（管理员用）

### 请求示例

#### 添加装备
```json
{
    "tid": 12345,
    "equipment_name": "闪光猎人称号",
    "expired_ts": 1735689600,
    "equipment_type": 0,
    "item_sale_type": 1,
    "effect_info": {
        "encounter_shine": 0.1,
        "catch_rate": 0.05
    }
}
```

#### 查询装备
```json
{
    "filter": {
        "tid": 12345,
        "equipment_type": 0,
        "status": 0
    },
    "limit": 10,
    "offset": 0
}
```

#### 装备到装饰槽
```json
{
    "equipment_id": 123,
    "equipment_type": 0
}
```

## 使用方法

### 初始化
在main.go中添加初始化调用：
```go
trainer.InitTrainerEquipment(ctx, logger, db)
```

### 注册RPC函数
在main.go的registrations map中添加：
```go
"RpcAddEquipment": trainer.RpcAddEquipment,
"RpcUpdateEquipment": trainer.RpcUpdateEquipment,
// ... 其他RPC函数
```

### 代码示例

#### 添加装备
```go
equipment := &MainServer.Equipment{
    Tid:           trainerId,
    EquipmentName: "测试称号",
    EquipmentType: MainServer.EquipmentType_equipment_title,
    EffectInfo: &MainServer.TrainerEquipmentEffect{
        EncounterShine: 0.1,
        ExpGain:        0.2,
    },
}

err := equipment.AddEquipment(ctx, tx, equipment)
```

#### 装备到装饰槽
```go
err := trainer.EquipToTrainerDecoration(ctx, logger, tx, trainer, equipmentId, equipmentType)
```

#### 计算装备效果
```go
totalEffects := trainer.CalculateEquipmentEffects(trainer)
```

## 数据流程

### 装备流程
1. 训练师获得装备（通过任务、购买等）
2. 装备存储在装备背包中
3. 训练师选择装备到对应的装饰槽位
4. 系统自动计算并应用装备效果
5. 装备效果影响游戏内的各种计算

### 交易流程
1. 训练师将装备上架到市场
2. 其他训练师可以浏览和购买
3. 交易完成后装备所有权转移
4. 原装备者失去装备，购买者获得装备

## 注意事项

### 数据库事务
- 除了表创建使用`*sql.DB`外，所有其他操作都使用`*sql.Tx`
- 确保在事务中进行装备相关的所有操作
- 适当处理事务回滚和提交

### 并发安全
- 装备装饰操作需要更新内存中的训练师数据
- 使用适当的锁机制确保数据一致性
- 装备强化和交易操作需要原子性

### 性能优化
- 使用索引优化查询性能
- 装备效果计算结果可以缓存
- 批量操作时使用批处理减少数据库交互

### 错误处理
- 验证装备是否属于操作者
- 检查装备槽位是否已被占用
- 验证装备类型和槽位类型匹配
- 处理装备不存在的情况
- 检查装备是否过期（过期装备无法装备）
- 验证专属道具的使用权限

## 扩展性

### 新装备类型
要添加新的装备类型：
1. 在proto定义中添加新的EquipmentType枚举值
2. 在TrainerDecoration中添加对应的装备槽位
3. 更新装备装饰逻辑以支持新类型

### 新装备效果
要添加新的装备效果：
1. 在TrainerEquipmentEffect中添加新的效果字段
2. 更新效果计算逻辑
3. 在游戏逻辑中应用新效果

### 装备套装
未来可以扩展支持装备套装系统：
- 检测装备组合
- 提供套装额外加成
- 套装效果叠加计算
