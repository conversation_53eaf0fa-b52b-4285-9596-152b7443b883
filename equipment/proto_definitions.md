# Equipment System Proto Definitions

以下是装备系统需要在另一个项目中实现的Protocol Buffer消息定义：

## 核心数据结构

### TrainerDecoration
```protobuf
message TrainerDecoration {
    TrainerEquipmentInfo equipment_title = 1;    // 称号装备
    TrainerEquipmentInfo equipment_card = 2;     // 卡片装备
    TrainerEquipmentInfo equipment_ride = 3;     // 坐骑装备
    TrainerEquipmentInfo equipment_pokeball = 4; // 精灵球装备
    TrainerEquipmentInfo equipment_badge = 5;    // 徽章装备
    TrainerEquipmentInfo equipment_amulet = 6;   // 护身符装备
}
```

### TrainerEquipmentInfo
```protobuf
message TrainerEquipmentInfo {
    int64 id = 1;                               // 装备ID (注意：字段名从equipment_id改为id)
    string equipment_name = 2;                   // 装备名称
    TrainerEquipmentEffect effect_info = 3;      // 装备效果
    int32 fortify_count = 4;                     // 强化次数
    int64 equip_ts = 5;                         // 装备时间戳
}
```

### TrainerEquipmentEffect
```protobuf
message TrainerEquipmentEffect {
    double encounter_shine = 1;                  // 遇到闪光宝可梦概率加成
    double breed_good_pokebaby = 2;             // 孵化优质宝可梦概率加成
    double breed_shine_pokebaby = 3;            // 孵化闪光宝可梦概率加成
    double breed_speed = 4;                     // 孵化速度加成
    double catch_rate = 5;                      // 捕获成功率加成
    double exp_gain = 6;                        // 经验获得加成
    double coin_gain = 7;                       // 金币获得加成
    double team_coin_gain = 8;                  // 队伍币获得加成
    double item_drop_rate = 9;                  // 道具掉落率加成
    double rare_item_drop_rate = 10;            // 稀有道具掉落率加成
    double battle_win_rate = 11;                // 战斗胜率加成
    double poke_hp_bonus = 12;                  // 宝可梦HP加成
    double poke_attack_bonus = 13;              // 宝可梦攻击力加成
    double poke_defense_bonus = 14;             // 宝可梦防御力加成
    double poke_speed_bonus = 15;               // 宝可梦速度加成
    double poke_special_attack_bonus = 16;      // 宝可梦特攻加成
    double poke_special_defense_bonus = 17;     // 宝可梦特防加成
    double critical_hit_rate = 18;              // 暴击率加成
}
```

### Equipment
```protobuf
message Equipment {
    int64 id = 1;                               // 装备ID
    int64 tid = 2;                              // 训练师ID
    string equipment_name = 3;                  // 装备名称
    int64 expired_ts = 4;                       // 过期时间戳（0为不过期）
    int64 price = 5;                            // 价格
    int64 team_coin = 6;                        // 队伍币价格
    TrainerTeam team_type = 7;                  // 队伍类型限制
    EquipmentStatus status = 8;                 // 装备状态
    EquipmentType equipment_type = 9;           // 装备类型
    ItemSaleType item_sale_type = 10;           // 表示是否是专属道具
    int32 fortify_count = 11;                   // 强化次数
    TrainerEquipmentEffect effect_info = 12;    // 装备效果
    int64 create_ts = 13;                       // 创建时间戳
    int64 update_ts = 14;                       // 更新时间戳
}
```

## 枚举类型

### EquipmentType
```protobuf
enum EquipmentType {
    equipment_title = 0;                        // 称号
    equipment_card = 1;                         // 卡片
    equipment_ride = 2;                         // 坐骑
    equipment_pokeball = 3;                     // 精灵球
    equipment_badge = 4;                        // 徽章
    equipment_amulet = 5;                       // 护身符
}
```

### EquipmentStatus
```protobuf
enum EquipmentStatus {
    EquipmentStatus_Normal = 0;                 // 正常状态
    EquipmentStatus_Sale = 1;                   // 上架销售状态
}
```

### ItemSaleType
```protobuf
enum ItemSaleType {
    ItemSaleType_Normal = 0;                    // 普通道具
    ItemSaleType_Exclusive = 1;                 // 专属道具
}
```

## 过滤器和参数

### EquipmentFilter
```protobuf
message EquipmentFilter {
    int64 id = 1;                               // 装备ID
    int64 tid = 2;                              // 训练师ID
    string equipment_name = 3;                  // 装备名称
    EquipmentType equipment_type = 4;           // 装备类型
    EquipmentStatus status = 5;                 // 装备状态
    TrainerTeam team_type = 6;                  // 队伍类型
    int64 min_price = 7;                        // 最低价格
    int64 max_price = 8;                        // 最高价格
    int32 min_fortify_count = 9;                // 最低强化次数
    int32 max_fortify_count = 10;               // 最高强化次数
    int64 update_ts = 11;                       // 更新时间戳过滤
    int64 expired_ts = 12;                      // 过期时间戳过滤
    ItemSaleType item_sale_type = 13;           // 道具销售类型过滤
    bool include_expired = 14;                  // 是否包含已过期装备
}
```

### QueryEquipmentsParam
```protobuf
message QueryEquipmentsParam {
    EquipmentFilter filter = 1;                 // 过滤条件
    int32 limit = 2;                            // 限制数量
    int32 offset = 3;                           // 偏移量
}
```

### SaleEquipmentParam
```protobuf
message SaleEquipmentParam {
    string equipment_name = 1;                  // 装备名称
    int32 count = 2;                            // 上架数量
    int64 price = 3;                            // 价格
    int64 special_coin = 4;                     // 特殊币价格
}
```

### UnsaleEquipmentParam
```protobuf
message UnsaleEquipmentParam {
    string equipment_name = 1;                  // 装备名称
    int32 count = 2;                            // 下架数量
}
```

### GetAllEquipmentsParam
```protobuf
message GetAllEquipmentsParam {
    int64 update_ts = 1;                        // 更新时间戳，大于0时只查询更新时间大于此值的记录
}
```

## 响应消息

### AddEquipmentResponse
```protobuf
message AddEquipmentResponse {
    bool success = 1;                           // 是否成功
    string message = 2;                         // 消息
    Equipment equipment = 3;                    // 添加的装备信息
}
```

### UpdateEquipmentResponse
```protobuf
message UpdateEquipmentResponse {
    bool success = 1;                           // 是否成功
    string message = 2;                         // 消息
    Equipment equipment = 3;                    // 更新后的装备信息
}
```

### DeleteEquipmentResponse
```protobuf
message DeleteEquipmentResponse {
    bool success = 1;                           // 是否成功
    string message = 2;                         // 消息
}
```

### SaleEquipmentResponse
```protobuf
message SaleEquipmentResponse {
    bool success = 1;                           // 是否成功
    string message = 2;                         // 消息
}
```

### UnsaleEquipmentResponse
```protobuf
message UnsaleEquipmentResponse {
    bool success = 1;                           // 是否成功
    string message = 2;                         // 消息
}
```

### GetAllEquipmentsResponse
```protobuf
message GetAllEquipmentsResponse {
    bool success = 1;                           // 是否成功
    repeated Equipment equipments = 2;          // 装备列表
}
```

### QueryEquipmentsResponse
```protobuf
message QueryEquipmentsResponse {
    bool success = 1;                           // 是否成功
    repeated Equipment equipments = 2;          // 装备列表
}
```

## 注意事项

1. 所有的proto消息都应该使用 `MainServer.` 前缀进行跨项目集成
2. 时间戳字段使用int64类型，表示Unix时间戳（秒）
3. 价格和金币字段使用int64类型以支持大数值
4. 装备效果使用double类型表示百分比加成（如0.1表示10%加成）
5. 强化次数使用int32类型，支持负数表示减少强化等级
6. 装备状态和类型使用枚举类型确保数据一致性
