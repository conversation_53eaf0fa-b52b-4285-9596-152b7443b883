{"configs": {"10001A": {"pokeTeamId": "10001A", "teamName": "道馆小刚", "pokes": [{"poke": {"id": "0", "tid": "0", "name": "aerodactyl", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "focussash", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "pressure", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "crunch", "pp_pro": 0, "pp_sub": 0}, {"name": "stoneedge", "pp_pro": 0, "pp_sub": 0}, {"name": "wideguard", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "slowbro", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "<PERSON><PERSON><PERSON>", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "regenerator", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "surf", "pp_pro": 0, "pp_sub": 0}, {"name": "psychic", "pp_pro": 0, "pp_sub": 0}, {"name": "icebeam", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "ninetales", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "focussash", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "flashfire", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "foulplay", "pp_pro": 0, "pp_sub": 0}, {"name": "heatwave", "pp_pro": 0, "pp_sub": 0}, {"name": "energyball", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "rampardos", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "rockgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "moldbreaker", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "headsmash", "pp_pro": 0, "pp_sub": 0}, {"name": "superpower", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "heracross", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "lifeorb", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "moxie", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "closecombat", "pp_pro": 0, "pp_sub": 0}, {"name": "megahorn", "pp_pro": 0, "pp_sub": 0}, {"name": "bulletseed", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "steelix", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "normalgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sturdy", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "explosion", "pp_pro": 0, "pp_sub": 0}, {"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "irontail", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}]}, "10001B": {"pokeTeamId": "10001B", "teamName": "道馆小刚", "pokes": [{"poke": {"id": "0", "tid": "0", "name": "golem", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "lie<PERSON><PERSON>", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sturdy", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "rockslide", "pp_pro": 0, "pp_sub": 0}, {"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "hammerarm", "pp_pro": 0, "pp_sub": 0}, {"name": "wideguard", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "toxicroak", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "focussash", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "dryskin", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "gunkshot", "pp_pro": 0, "pp_sub": 0}, {"name": "crosschop", "pp_pro": 0, "pp_sub": 0}, {"name": "suckerpunch", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "pinsir", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "lifeorb", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "moxie", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "x-scissor", "pp_pro": 0, "pp_sub": 0}, {"name": "stoneedge", "pp_pro": 0, "pp_sub": 0}, {"name": "closecombat", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "rampardos", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "rockgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "moldbreaker", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "headsmash", "pp_pro": 0, "pp_sub": 0}, {"name": "crunch", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "sudowoodo", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "<PERSON><PERSON><PERSON>", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sturdy", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "woodhammer", "pp_pro": 0, "pp_sub": 0}, {"name": "headsmash", "pp_pro": 0, "pp_sub": 0}, {"name": "suckerpunch", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "steelix", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "normalgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sturdy", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "explosion", "pp_pro": 0, "pp_sub": 0}, {"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "irontail", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}]}, "10001C": {"pokeTeamId": "10001C", "teamName": "道馆小刚", "pokes": [{"poke": {"id": "0", "tid": "0", "name": "tyranitar", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "smoothrock", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sandstream", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "stoneedge", "pp_pro": 0, "pp_sub": 0}, {"name": "crunch", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "swampert", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "focussash", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "torrent", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "aquatail", "pp_pro": 0, "pp_sub": 0}, {"name": "icepunch", "pp_pro": 0, "pp_sub": 0}, {"name": "wideguard", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "crobat", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "lifeorb", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "infiltrator", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "bravebird", "pp_pro": 0, "pp_sub": 0}, {"name": "heatwave", "pp_pro": 0, "pp_sub": 0}, {"name": "sludgebomb", "pp_pro": 0, "pp_sub": 0}, {"name": "gigadrain", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "forretress", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "lumberry", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sturdy", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "pin<PERSON><PERSON>le", "pp_pro": 0, "pp_sub": 0}, {"name": "gyroball", "pp_pro": 0, "pp_sub": 0}, {"name": "rest", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "rhyperior", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "pass<PERSON>berry", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "solidrock", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "rockwrecker", "pp_pro": 0, "pp_sub": 0}, {"name": "megahorn", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "steelix", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "focussash", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sheerforce", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "rockslide", "pp_pro": 0, "pp_sub": 0}, {"name": "irontail", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}]}, "10001D": {"pokeTeamId": "10001D", "teamName": "道馆小刚", "pokes": [{"poke": {"id": "0", "tid": "0", "name": "aerodactyl", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "powerherb", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "pressure", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "aquatail", "pp_pro": 0, "pp_sub": 0}, {"name": "stoneedge", "pp_pro": 0, "pp_sub": 0}, {"name": "skyattack", "pp_pro": 0, "pp_sub": 0}, {"name": "wideguard", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "ludico<PERSON>", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "grassgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "swiftswim", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "surf", "pp_pro": 0, "pp_sub": 0}, {"name": "gigadrain", "pp_pro": 0, "pp_sub": 0}, {"name": "icebeam", "pp_pro": 0, "pp_sub": 0}, {"name": "raindance", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "kabutops", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "focussash", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "swiftswim", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "stoneedge", "pp_pro": 0, "pp_sub": 0}, {"name": "aquatail", "pp_pro": 0, "pp_sub": 0}, {"name": "superpower", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "relicanth", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "rockgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "swiftswim", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "headsmash", "pp_pro": 0, "pp_sub": 0}, {"name": "aquatail", "pp_pro": 0, "pp_sub": 0}, {"name": "zenheadbutt", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "omastar", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "lifeorb", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "swiftswim", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "muddywater", "pp_pro": 0, "pp_sub": 0}, {"name": "icebeam", "pp_pro": 0, "pp_sub": 0}, {"name": "earthpower", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "steelix", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "groundgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sturdy", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "aquatail", "pp_pro": 0, "pp_sub": 0}, {"name": "irontail", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}]}, "10001E": {"pokeTeamId": "10001E", "teamName": "道馆小刚", "pokes": [{"poke": {"id": "0", "tid": "0", "name": "golem", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "groundgem", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sturdy", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "stoneedge", "pp_pro": 0, "pp_sub": 0}, {"name": "hammerarm", "pp_pro": 0, "pp_sub": 0}, {"name": "wideguard", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "tyranitar", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "smoothrock", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sandstream", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "rockslide", "pp_pro": 0, "pp_sub": 0}, {"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "crunch", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "sandslash", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "brightpowder", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sandveil", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "stoneedge", "pp_pro": 0, "pp_sub": 0}, {"name": "x-scissor", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "shuckle", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "leftovers", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "contrary", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "shellsmash", "pp_pro": 0, "pp_sub": 0}, {"name": "helpinghand", "pp_pro": 0, "pp_sub": 0}, {"name": "rest", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "chansey", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "eviolite", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "healer", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "seismictoss", "pp_pro": 0, "pp_sub": 0}, {"name": "toxic", "pp_pro": 0, "pp_sub": 0}, {"name": "wish", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}, {"poke": {"id": "0", "tid": "0", "name": "steelix", "nick_name": "", "ball_name": "", "item_info": {"inventory_id": "0", "tid": "0", "item_name": "lifeorb", "quantity": 0, "item_sale_type": 0, "team_type": 0}, "ability": "sheerforce", "evs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "ivs": {"hp": 0, "atk": 0, "def": 0, "spa": 0, "spd": 0, "spe": 0}, "transaction_type": 0, "moves": [{"name": "rockslide", "pp_pro": 0, "pp_sub": 0}, {"name": "earthquake", "pp_pro": 0, "pp_sub": 0}, {"name": "irontail", "pp_pro": 0, "pp_sub": 0}, {"name": "protect", "pp_pro": 0, "pp_sub": 0}], "level": 0, "nature": 2, "experience": "0", "egg": false, "shiny": 0, "gender": 0, "hp_sub": 0, "happiness": 0, "release": false, "breedCount": 0, "create_ts": "0", "update_ts": "0"}, "pokeBoostStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "pokeBoostMinStat": {"hp": 0, "attack": 0, "defense": 0, "specialAttack": 0, "specialDefense": 0, "speed": 0, "accuracy": 0, "evasion": 0}, "immunityTypes": []}]}, "10002A": {"pokeTeamId": "10002A", "teamName": "道馆小霞", "pokes": []}, "10002B": {"pokeTeamId": "10002B", "teamName": "道馆小霞", "pokes": []}, "10002C": {"pokeTeamId": "10002C", "teamName": "道馆小霞", "pokes": []}}}