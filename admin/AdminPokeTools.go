package admin

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"go-nakama-poke/email"
	"go-nakama-poke/poke"
	"go-nakama-poke/proto/MainServer"
	"go-nakama-poke/trainer"
	"go-nakama-poke/user"
	"sort"

	"github.com/heroiclabs/nakama-common/runtime"
)

// RpcAdminSaveAllPokemonToTrainer 将图鉴中所有宝可梦保存给指定训练师
func RpcAdminSaveAllPokemonToTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证管理员权限
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}

	// 解析请求数据
	var request struct {
		TrainerID int64 `json:"trainer_id"`
	}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", runtime.NewError("无效的请求数据", 400)
	}

	// 获取训练师信息
	trainerInfo, err := trainer.SelectTrainerProto(ctx, db, request.TrainerID)
	if err != nil {
		return "", fmt.Errorf("获取训练师信息失败: %v", err)
	}

	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()

	// 获取所有宝可梦信息
	pokedex := poke.GetAllPokemonInfo()
	successCount := 0
	failCount := 0
	// 先拿到所有值，放到 slice
	var pokemonList []*MainServer.PSPokemonData
	for key, data := range pokedex {
		data.Name = key
		if data.BaseSpecies == "" {
			pokemonList = append(pokemonList, data)
		}
	}

	// 排序
	sort.Slice(pokemonList, func(i, j int) bool {
		return pokemonList[i].Num < pokemonList[j].Num
	})
	logger.Info("正常创建宝可梦")
	for _, p := range pokemonList {
		pokeName := p.Name
		createPokeInfo := &MainServer.CreatePokeInfo{
			Name:  pokeName,
			Level: 50,
		}
		// 创建一个新的宝可梦实例
		newPoke, err := poke.CreatePoke(ctx, tx, createPokeInfo) // 默认等级50
		if err != nil {
			logger.Error("创建宝可梦失败 %s: %v", pokeName, err)
			failCount++
			continue
		}

		// 设置训练师ID
		newPoke.Tid = request.TrainerID
		err = trainer.TakeWildPoke(ctx, logger, tx, trainerInfo, newPoke)
		if err != nil {
			logger.Error("保存到盒子失败 %s: %v", pokeName, err)
			failCount++
			continue
		}

		successCount++
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}

	result := map[string]interface{}{
		"success_count": successCount,
		"fail_count":    failCount,
		"total":         len(pokedex),
	}
	resultJSON, _ := json.Marshal(result)
	logger.Info("创建宝可梦完成")
	return string(resultJSON), nil
}

// RpcAdminSavePokemonToTrainer 将指定宝可梦保存给训练师
func RpcAdminSavePokemonToTrainer(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 验证管理员权限
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}

	// 解析请求数据
	var request struct {
		TrainerID int64  `json:"trainer_id"`
		PokeName  string `json:"poke_name"`
		Level     int    `json:"level"`
		Shiny     int32  `json:"shiny"`
	}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", runtime.NewError("无效的请求数据", 400)
	}

	// 验证宝可梦是否存在
	if _, exists := poke.GetPokemonInfo(request.PokeName); !exists {
		return "", runtime.NewError("宝可梦不存在", 404)
	}

	// 获取训练师信息
	trainerInfo, err := trainer.SelectTrainerProto(ctx, db, request.TrainerID)
	if err != nil {
		return "", fmt.Errorf("获取训练师信息失败: %v", err)
	}

	// 开启事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return "", fmt.Errorf("开启事务失败: %v", err)
	}
	defer tx.Rollback()
	createPokeInfo := &MainServer.CreatePokeInfo{
		Name:  request.PokeName,
		Level: int32(request.Level),
	}
	// 创建宝可梦
	newPoke, err := poke.CreatePoke(ctx, tx, createPokeInfo)
	if err != nil {
		return "", fmt.Errorf("创建宝可梦失败: %v", err)
	}
	// 保存到box中
	err = trainer.TakeWildPoke(ctx, logger, tx, trainerInfo, newPoke)
	if err != nil {
		return "", fmt.Errorf("保存到盒子失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return "", fmt.Errorf("提交事务失败: %v", err)
	}

	return "保存成功", nil
}

// RpcSendEmailsToUsers 给多个指定用户发送邮件的RPC处理函数
func RpcSendEmailsToUsers(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	/*
		"{"receiver_ids": [1,2,3], "contents": [{"language": "zh-CN", "title": "你好", "content": "你好, 这是邮件内容"},
		{"language": "en-US", "title": "Hello", "content": "Hello, this is the email content"}],
		"attachments": {"items": {"item_name": "abilityshield", "count": 10}}}"
	*/
	// 获取当前用户ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}

	// 解析请求
	var request struct {
		ReceiverIds []int64                     `json:"receiver_ids"`
		Contents    []*MainServer.EmailLanguage `json:"contents"`
		Attachments *MainServer.EmailAttachment `json:"attachments"`
	}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("开始事务失败: %v", err)
		return "", runtime.NewError("开始事务失败", 500)
	}
	defer tx.Rollback()

	// 发送邮件
	if err := email.SendEmailsToUsers(ctx, logger, tx, nk, 0, request.ReceiverIds, request.Contents, request.Attachments); err != nil {
		return "", err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("提交事务失败: %v", err)
		return "", runtime.NewError("提交事务失败", 500)
	}

	return "{}", nil
}

// RpcSendEmailToAllUsers 给所有用户发送邮件的RPC处理函数
func RpcSendEmailToAllUsers(ctx context.Context, logger runtime.Logger, db *sql.DB, nk runtime.NakamaModule, payload string) (string, error) {
	// 获取当前用户ID
	userID := ctx.Value(runtime.RUNTIME_CTX_USER_ID).(string)
	isAdmin, err := user.IsAdmin(ctx, nk, userID)
	if err != nil || !isAdmin {
		return "", runtime.NewError("没有管理员权限", 403)
	}
	// 解析请求
	var request struct {
		Contents    []*MainServer.EmailLanguage `json:"contents"`
		Attachments *MainServer.EmailAttachment `json:"attachments"`
	}
	if err := json.Unmarshal([]byte(payload), &request); err != nil {
		return "", runtime.NewError("无效的请求格式", 400)
	}

	// 开始事务
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		logger.Error("开始事务失败: %v", err)
		return "", runtime.NewError("开始事务失败", 500)
	}
	defer tx.Rollback()
	receiverIds, err := trainer.GetAllTrainerIds(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("获取所有训练师ID失败: %v", err)
	}
	// 发送邮件给所有用户
	if err := email.SendEmailsToUsers(ctx, logger, tx, nk, 0, receiverIds, request.Contents, request.Attachments); err != nil {
		return "", err
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logger.Error("提交事务失败: %v", err)
		return "", runtime.NewError("提交事务失败", 500)
	}

	return "{}", nil
}
